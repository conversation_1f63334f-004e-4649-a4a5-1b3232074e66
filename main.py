try:
    import discord

    print(f"✅ Discord.py-self версия {discord.__version__}")
except ImportError:
    print("❌ Discord библиотека не установлена!")
    exit(1)

import asyncio
import os
import sqlite3
import time
import signal
import sys
from datetime import datetime, timezone, timedelta
from typing import Set, Dict, List, Optional, Tuple
from dotenv import load_dotenv
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from collections import defaultdict, deque
import json
import random
import re
import heapq
from dataclasses import dataclass
from enum import Enum

# Расширенное логирование
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ultra_fast_monitor.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# Импорт Bloom filter для оптимизации памяти
try:
    from pybloom_live import BloomFilter
    BLOOM_FILTER_AVAILABLE = True
except ImportError:
    BLOOM_FILTER_AVAILABLE = False
    logger.warning("pybloom-live не установлен. Bloom filter недоступен.")


class SourceStats:
    """Статистика по источникам данных"""

    def __init__(self, name: str):
        self.name = name
        self.members_found = 0
        self.time_taken = 0.0
        self.items_processed = 0
        self.errors = 0
        self.start_time = 0

    def start(self):
        self.start_time = time.time()

    def finish(self):
        self.time_taken = time.time() - self.start_time

    def add_member(self):
        self.members_found += 1

    def add_processed(self, count=1):
        self.items_processed += count

    def add_error(self):
        self.errors += 1

    @property
    def efficiency(self) -> float:
        try:
            members_found = getattr(self, 'members_found', 0)
            time_taken = getattr(self, 'time_taken', 0)
            return members_found / time_taken if time_taken > 0 else 0
        except Exception:
            return 0

    def __str__(self):
        try:
            return (f"{self.name:20} | {getattr(self, 'members_found', 0):4d} участников | "
                    f"{getattr(self, 'time_taken', 0):6.1f}с | {getattr(self, 'efficiency', 0):6.1f} уч/с | "
                    f"{getattr(self, 'items_processed', 0):5d} обработано | {getattr(self, 'errors', 0):2d} ошибок")
        except Exception as e:
            return f"{self.name:20} | ОШИБКА ВЫВОДА: {e}"


class AdaptiveScanningStrategy:
    """Динамический выбор источников данных на основе эффективности"""
    
    def __init__(self):
        # Инициализируем веса для всех источников
        self.source_weights = {
            'reaction_scan': 1.0,
            'message_scan': 1.2,
            'memberlist_scan': 1.5,
            'system_messages': 1.3,
            'events_scan': 1.4,
            'notifications': 1.1,
            'deep_problem_scan': 1.7,
            'full_memberlist': 1.8,
            'enhanced_memberlist': 1.6,
            'cache_scan': 1.0,
            'voice_scan': 1.1,
            'threads_forums': 1.2,
            'embeds': 1.0,
            'invites_roles': 1.1,
            'pinned_messages': 1.0,
            'integrations': 1.0,
            'audit_log': 1.1,
            'activities': 1.0,
            'application_commands': 1.0,
            'activity_patterns': 1.1
        }
        self.source_success = defaultdict(int)
        self.source_failures = defaultdict(int)
        self.source_history = defaultdict(list)  # История эффективности
        self.last_update = defaultdict(float)
    
    def update(self, source_name: str, efficiency: float, success: bool = True):
        """Обновление весов на основе эффективности"""
        current_time = time.time()
        
        # Обновляем историю
        self.source_history[source_name].append({
            'efficiency': efficiency,
            'timestamp': current_time,
            'success': success
        })
        
        # Ограничиваем историю последними 10 записями
        if len(self.source_history[source_name]) > 10:
            self.source_history[source_name] = self.source_history[source_name][-10:]
        
        # Вычисляем среднюю эффективность
        recent_efficiencies = [entry['efficiency'] for entry in self.source_history[source_name][-5:]]
        avg_efficiency = sum(recent_efficiencies) / len(recent_efficiencies) if recent_efficiencies else 0
        
        # Обновляем веса с экспоненциальным сглаживанием
        alpha = 0.3  # Коэффициент сглаживания
        self.source_weights[source_name] = (
            alpha * avg_efficiency + 
            (1 - alpha) * self.source_weights[source_name]
        )
        
        # Учет успехов/неудач
        if success:
            self.source_success[source_name] += 1
        else:
            self.source_failures[source_name] += 1
            
        # Автоматическая коррекция весов на основе успешности
        total_attempts = self.source_success[source_name] + self.source_failures[source_name]
        if total_attempts > 0:
            success_rate = self.source_success[source_name] / total_attempts
            self.source_weights[source_name] *= (0.5 + 0.5 * success_rate)
        
        self.last_update[source_name] = current_time
    
    def get_top_sources(self, count=8) -> List[str]:
        """Получение наиболее эффективных источников"""
        return sorted(self.source_weights.keys(), 
                     key=lambda x: self.source_weights[x], 
                     reverse=True)[:count]
    
    def get_priority_queue(self) -> List[str]:
        """Формирование очереди приоритетов"""
        return self.get_top_sources() + [
            s for s in self.source_weights 
            if s not in self.get_top_sources()
        ]
    
    def get_source_stats(self) -> Dict[str, dict]:
        """Получение статистики по источникам"""
        stats = {}
        for source in self.source_weights:
            total_attempts = self.source_success[source] + self.source_failures[source]
            success_rate = self.source_success[source] / total_attempts if total_attempts > 0 else 0
            
            stats[source] = {
                'weight': self.source_weights[source],
                'success_rate': success_rate,
                'total_attempts': total_attempts,
                'last_update': self.last_update[source]
            }
        return stats
    
    def reset_source(self, source_name: str):
        """Сброс статистики для источника"""
        self.source_weights[source_name] = 1.0
        self.source_success[source_name] = 0
        self.source_failures[source_name] = 0
        self.source_history[source_name] = []
        self.last_update[source_name] = 0


class OptimizedBloomFilter:
    """Динамически расширяемый Bloom Filter"""
    
    def __init__(self, initial_capacity=10000, error_rate=0.01, growth_factor=2):
        self.capacity = initial_capacity
        self.error_rate = error_rate
        self.growth_factor = growth_factor
        self.filter = BloomFilter(capacity=initial_capacity, error_rate=error_rate)
        self.count = 0
        self.expansion_count = 0
        
    def add(self, item):
        if self.count >= self.capacity * 0.8:
            self._expand()
        self.filter.add(item)
        self.count += 1
        
    def __contains__(self, item):
        return item in self.filter
    
    def _expand(self):
        new_capacity = int(self.capacity * self.growth_factor)
        new_filter = BloomFilter(capacity=new_capacity, error_rate=self.error_rate)
        
        # Переиндексация существующих элементов
        for item in self.filter:
            new_filter.add(item)
            
        self.filter = new_filter
        self.capacity = new_capacity
        self.expansion_count += 1
        logger.info(f"🔄 Bloom Filter расширен до {new_capacity} элементов (расширение #{self.expansion_count})")
    
    def get_stats(self) -> dict:
        """Получение статистики Bloom Filter"""
        return {
            'capacity': self.capacity,
            'count': self.count,
            'error_rate': self.error_rate,
            'expansion_count': self.expansion_count,
            'utilization': self.count / self.capacity if self.capacity > 0 else 0
        }


class SmartCache:
    """Интеллектуальное кэширование запросов"""
    
    def __init__(self, ttl=300, max_size=1000):
        self.cache = {}
        self.ttl = ttl
        self.max_size = max_size
        self.lock = asyncio.Lock()
        self.access_count = defaultdict(int)
    
    async def get(self, key, coroutine):
        async with self.lock:
            current_time = time.time()
            
            if key in self.cache:
                entry = self.cache[key]
                if current_time - entry['timestamp'] < self.ttl:
                    self.access_count[key] += 1
                    return entry['data']
                else:
                    # Удаляем устаревшую запись
                    del self.cache[key]
                    del self.access_count[key]
            
            # Выполнение и кэширование
            try:
                data = await coroutine
                self.cache[key] = {
                    'data': data,
                    'timestamp': current_time
                }
                self.access_count[key] = 1
                
                # Очистка кэша при превышении размера
                if len(self.cache) > self.max_size:
                    self._cleanup()
                
                return data
            except Exception as e:
                logger.error(f"❌ Ошибка кэширования для ключа {key}: {e}")
                raise
    
    def _cleanup(self):
        """Очистка кэша от наименее используемых записей"""
        if len(self.cache) <= self.max_size // 2:
            return
        
        # Сортируем по количеству обращений и времени последнего доступа
        entries = []
        current_time = time.time()
        
        for key, entry in self.cache.items():
            score = self.access_count[key] / (current_time - entry['timestamp'] + 1)
            entries.append((score, key))
        
        # Удаляем 50% наименее популярных записей
        entries.sort()
        to_remove = len(entries) // 2
        
        for _, key in entries[:to_remove]:
            del self.cache[key]
            del self.access_count[key]
        
        logger.info(f"🧹 Кэш очищен: удалено {to_remove} записей")
    
    def get_stats(self) -> dict:
        """Получение статистики кэша"""
        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'ttl': self.ttl,
            'total_accesses': sum(self.access_count.values())
        }


class SmartRateLimiter:
    """УЛУЧШЕННЫЙ умный rate limiter с глобальными ограничениями Discord API"""

    def __init__(self):
        # Обновленные лимиты на основе анализа Discord API
        self.limits = {
            'message_scan': (30, 60),      # Уменьшено с 50 до 30 (безопаснее)
            'reaction_scan': (15, 60),     # Уменьшено с 20 до 15 (строже)
            'thread_scan': (20, 60),       # Уменьшено с 30 до 20
            'forum_scan': (15, 60),        # Уменьшено с 25 до 15
            'invite_scan': (5, 60),        # Уменьшено с 10 до 5 (очень строго)
            'member_scan': (3, 60),        # Уменьшено с 5 до 3 (критически строго)
            'memberlist_scan': (1, 600),   # Уменьшено с 2 до 1 запрос в 10 минут
            'audit_log_scan': (2, 300),    # Новый лимит для аудит лога
            'pinned_scan': (10, 60),       # Новый лимит для закрепленных сообщений
            'api_fetch': (5, 300),         # Новый лимит для API запросов
            'global_api': (100, 60),       # ГЛОБАЛЬНЫЙ лимит Discord API
        }
        self.buckets = defaultdict(deque)
        self.locks = defaultdict(threading.Lock)

        # Глобальный счетчик для всех API запросов
        self.global_requests = deque()
        self.global_lock = threading.Lock()

    async def wait_if_needed(self, operation_type: str):
        """УЛУЧШЕННОЕ ожидание с глобальным rate limiting Discord API"""
        # Сначала проверяем глобальный лимит Discord API
        await self._check_global_rate_limit()

        if operation_type not in self.limits:
            await asyncio.sleep(random.uniform(0.2, 0.5))  # Увеличенная базовая задержка
            return

        max_requests, period = self.limits[operation_type]
        now = time.time()

        with self.locks[operation_type]:
            # Очищаем старые запросы
            while (self.buckets[operation_type] and
                   now - self.buckets[operation_type][0] > period):
                self.buckets[operation_type].popleft()

            # Проверяем лимит
            if len(self.buckets[operation_type]) >= max_requests:
                oldest = self.buckets[operation_type][0]
                wait_time = period - (now - oldest) + random.uniform(1.0, 3.0)  # Увеличенная задержка
                if wait_time > 0:
                    logger.debug(f"Rate limit {operation_type}: ожидание {wait_time:.1f}с")
                    await asyncio.sleep(wait_time)

            # Добавляем текущий запрос
            self.buckets[operation_type].append(now)

        # Увеличенная базовая задержка для безопасности
        await asyncio.sleep(random.uniform(0.3, 0.8))

    async def _check_global_rate_limit(self):
        """Проверка глобального лимита Discord API"""
        max_global, period = self.limits['global_api']
        now = time.time()

        with self.global_lock:
            # Очищаем старые глобальные запросы
            while (self.global_requests and
                   now - self.global_requests[0] > period):
                self.global_requests.popleft()

            # Проверяем глобальный лимит
            if len(self.global_requests) >= max_global:
                oldest = self.global_requests[0]
                wait_time = period - (now - oldest) + random.uniform(2.0, 5.0)
                if wait_time > 0:
                    logger.warning(f"🚨 ГЛОБАЛЬНЫЙ rate limit Discord API: ожидание {wait_time:.1f}с")
                    await asyncio.sleep(wait_time)

            # Добавляем текущий глобальный запрос
            self.global_requests.append(now)

    def update_limits_based_on_guild(self, guild):
        """Адаптивные лимиты на основе размера сервера"""
        size = guild.member_count

        if size > 10000:
            # Строже для больших серверов
            self.limits['memberlist_scan'] = (1, 600)  # 1 запрос в 10 минут
            self.limits['message_scan'] = (30, 60)  # 30 запросов в минуту
            self.limits['reaction_scan'] = (15, 60)  # 15 запросов в минуту
            logger.info(f"🔧 Адаптивные лимиты для большого сервера ({size:,} участников)")

        elif size > 5000:
            # Средние лимиты для средних серверов
            self.limits['memberlist_scan'] = (2, 300)  # 2 запроса в 5 минут
            self.limits['message_scan'] = (40, 60)  # 40 запросов в минуту
            self.limits['reaction_scan'] = (18, 60)  # 18 запросов в минуту
            logger.info(f"🔧 Адаптивные лимиты для среднего сервера ({size:,} участников)")

        elif size < 1000:
            # Выше лимиты для маленьких серверов
            self.limits['memberlist_scan'] = (5, 300)  # 5 запросов в 5 минут
            self.limits['message_scan'] = (100, 60)  # 100 запросов в минуту
            self.limits['reaction_scan'] = (30, 60)  # 30 запросов в минуту
            logger.info(f"🔧 Адаптивные лимиты для малого сервера ({size:,} участников)")

        else:
            # Стандартные лимиты для обычных серверов
            logger.info(f"🔧 Стандартные лимиты для сервера ({size:,} участников)")

    def adaptive_scanning_params(self, guild=None, mode="normal"):
        """Адаптивные параметры сканирования на основе размера сервера"""
        if guild is None:
            # Возвращаем параметры по умолчанию
            return {
                "max_channels": 10,
                "max_messages": 100,
                "max_reactions": 50,
                "scan_interval": 180,  # 3 минуты
                "batch_size": 100,
                "retry_attempts": 3
            }
        
        member_count = guild.member_count
        
        # TURBO MODE: Агрессивные настройки для максимальной скорости
        if mode == "turbo":
            if member_count > 10000:
                return {
                    "max_channels": 15,  # Увеличено в 3x
                    "max_messages": 150,  # Увеличено в 3x
                    "max_reactions": 60,  # Увеличено в 3x
                    "scan_interval": 120,  # Уменьшено в 2.5x
                    "batch_size": 200,    # Увеличено в 4x
                    "retry_attempts": 1   # Уменьшено для скорости
                }
            elif member_count > 5000:
                return {
                    "max_channels": 30,  # Увеличено в 3x
                    "max_messages": 300,  # Увеличено в 3x
                    "max_reactions": 150, # Увеличено в 3x
                    "scan_interval": 60,  # Уменьшено в 3x
                    "batch_size": 300,    # Увеличено в 3x
                    "retry_attempts": 2   # Уменьшено для скорости
                }
            else:
                return {
                    "max_channels": 45,  # Увеличено в 3x
                    "max_messages": 600,  # Увеличено в 3x
                    "max_reactions": 300, # Увеличено в 3x
                    "scan_interval": 30,  # Уменьшено в 4x
                    "batch_size": 450,    # Увеличено в 3x
                    "retry_attempts": 2   # Уменьшено для скорости
                }
        
        # NORMAL MODE: Стандартные настройки
        if member_count > 10000:
            return {
                "max_channels": 20,  # Увеличено в 4x
                "max_messages": 200,  # Увеличено в 4x
                "max_reactions": 100, # Увеличено в 5x
                "scan_interval": 300,  # 5 минут
                "batch_size": 200,    # Увеличено в 4x
                "retry_attempts": 3   # Увеличено
            }
        elif member_count > 5000:
            return {
                "max_channels": 15,   # Увеличено в 1.5x
                "max_messages": 150,  # Увеличено в 1.5x
                "max_reactions": 75,  # Увеличено в 1.5x
                "scan_interval": 180,  # 3 минуты
                "batch_size": 150,    # Увеличено в 1.5x
                "retry_attempts": 3
            }
        else:
            return {
                "max_channels": 20,   # Увеличено в 1.3x
                "max_messages": 300,  # Увеличено в 1.5x
                "max_reactions": 150, # Увеличено в 1.5x
                "scan_interval": 120,  # 2 минуты
                "batch_size": 200,    # Увеличено в 1.3x
                "retry_attempts": 3
            }


class EnhancedUltraFastScanner:
    """Расширенный ультра-быстрый сканер со всеми источниками данных"""

    def __init__(self, guild_id: int):
        self.guild_id = guild_id
        self.db_path = f"enhanced_ultrafast_members_{guild_id}.db"
        self.found_members = set()
        self.client = None
        self.guild = None
        self.start_time = 0
        self.monitoring_active = False
        self.shutdown_event = threading.Event()

        # Статистика по источникам
        self.source_stats = {}
        self.rate_limiter = SmartRateLimiter()

        # Счетчик каналов для отслеживания новых
        self._last_channel_count = 0

        # Кэширование прав доступа
        self.permission_cache = {}
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        
        # Блокировка для безопасного доступа к общим ресурсам
        self.member_lock = asyncio.Lock()
        
        # Bloom filter будет инициализирован после получения объекта гильдии
        # Увеличиваем начальную емкость для больших серверов
        self.bloom_capacity = 100000  # Увеличено с 10000
        self.bloom_filter = None
        
        # Инициализация адаптивной стратегии и кэша
        self.scan_strategy = AdaptiveScanningStrategy()
        self.request_cache = SmartCache(ttl=600, max_size=2000)
        logger.info("✅ Адаптивная стратегия и интеллектуальный кэш инициализированы")

        # Инициализация БД
        self._init_db()

        # Обработка сигналов для graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def has_permission(self, channel, permission: str) -> bool:
        """Кэшированная проверка прав доступа"""
        cache_key = f"{channel.id}_{permission}"
        if cache_key not in self.permission_cache:
            self.permission_cache[cache_key] = getattr(
                channel.permissions_for(self.guild.me),
                permission,
                False
            )
        return self.permission_cache[cache_key]

    def is_member_found(self, member_id: int) -> bool:
        """Проверка, найден ли участник (с использованием оптимизированного Bloom filter)"""
        if self.bloom_filter:
            return member_id in self.bloom_filter
        return member_id in self.found_members
    
    async def mark_member_found(self, member_id: int):
        """Отметить участника как найденного (с использованием оптимизированного Bloom filter)"""
        async with self.member_lock:
            # Добавляем только в found_members, Bloom Filter будет обновлен при сохранении
            self.found_members.add(member_id)
    
    def get_bloom_filter_stats(self) -> dict:
        """Получение статистики оптимизированного Bloom filter"""
        if self.bloom_filter:
            return self.bloom_filter.get_stats()
        return {"enabled": False}
    
    def optimize_bloom_filter(self):
        """Оптимизация Bloom filter при необходимости"""
        if self.bloom_filter:
            stats = self.bloom_filter.get_stats()
            if stats['utilization'] > 0.8:
                logger.info(f"🔄 Bloom Filter автоматически расширен (использование: {stats['utilization']:.1%})")

    def _signal_handler(self, signum, frame):
        """Обработчик сигналов для graceful shutdown"""
        logger.info("\n🛑 Получен сигнал остановки. Завершаю работу...")
        self.monitoring_active = False
        self.shutdown_event.set()
        # Не вызываем sys.exit() - это прерывает асинхронные задачи

    def _init_db(self):
        """Расширенная инициализация БД"""
        conn = sqlite3.connect(self.db_path)

        # Таблица участников
        conn.execute('''
                     CREATE TABLE IF NOT EXISTS members
                     (
                         user_id
                         INTEGER
                         PRIMARY
                         KEY,
                         username
                         TEXT,
                         display_name
                         TEXT,
                         found_method
                         TEXT,
                         channel_name
                         TEXT,
                         first_seen
                         TEXT,
                         last_seen
                         TEXT,
                         discovery_count
                         INTEGER
                         DEFAULT
                         1
                     )
                     ''')

        # Таблица статистики источников
        conn.execute('''
                     CREATE TABLE IF NOT EXISTS source_stats
                     (
                         id
                         INTEGER
                         PRIMARY
                         KEY
                         AUTOINCREMENT,
                         source_name
                         TEXT,
                         members_found
                         INTEGER,
                         time_taken
                         REAL,
                         items_processed
                         INTEGER,
                         errors
                         INTEGER,
                         efficiency
                         REAL,
                         timestamp
                         TEXT
                     )
                     ''')

        # Таблица мониторинга в реальном времени
        conn.execute('''
                     CREATE TABLE IF NOT EXISTS realtime_discoveries
                     (
                         id
                         INTEGER
                         PRIMARY
                         KEY
                         AUTOINCREMENT,
                         user_id
                         INTEGER,
                         username
                         TEXT,
                         channel_name
                         TEXT,
                         message_content
                         TEXT,
                         discovery_type
                         TEXT,
                         timestamp
                         TEXT
                     )
                     ''')

        # Таблица для мета-аналитики
        conn.execute('''
                     CREATE TABLE IF NOT EXISTS discovery_metrics
                     (
                         source
                         TEXT
                         PRIMARY
                         KEY,
                         efficiency
                         REAL,
                         avg_new_members
                         REAL,
                         last_run
                         TEXT,
                         total_runs
                         INTEGER
                         DEFAULT
                         0,
                         success_rate
                         REAL
                         DEFAULT
                         0.0
                     )
                     ''')

        # Таблица для проблемных участников
        conn.execute('''
                     CREATE TABLE IF NOT EXISTS problem_members
                     (
                         user_id
                         INTEGER
                         PRIMARY
                         KEY,
                         username
                         TEXT,
                         last_seen
                         TEXT,
                         detection_methods
                         TEXT,
                         flags
                         TEXT,
                         trust_level
                         TEXT
                         DEFAULT
                         'medium'
                     )
                     ''')

        # Таблица для адаптивных настроек
        conn.execute('''
                     CREATE TABLE IF NOT EXISTS adaptive_settings
                     (
                         setting_name
                         TEXT
                         PRIMARY
                         KEY,
                         setting_value
                         TEXT,
                         last_updated
                         TEXT,
                         performance_impact
                         REAL
                     )
                     ''')

        # Таблица ролей участников
        conn.execute('''
                     CREATE TABLE IF NOT EXISTS member_roles
                     (
                         user_id INTEGER,
                         role_id INTEGER,
                         role_name TEXT,
                         role_color INTEGER,
                         role_position INTEGER,
                         PRIMARY KEY (user_id, role_id),
                         FOREIGN KEY (user_id) REFERENCES members(user_id)
                     )
                     ''')

        # Индексы для производительности
        conn.execute('CREATE INDEX IF NOT EXISTS idx_members_method ON members(found_method)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_members_timestamp ON members(first_seen)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_realtime_timestamp ON realtime_discoveries(timestamp)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_discovery_metrics_efficiency ON discovery_metrics(efficiency)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_member_roles_user_id ON member_roles(user_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_member_roles_role_id ON member_roles(role_id)')

        conn.commit()
        conn.close()

    def save_members_batch(self, members_data: List[tuple]) -> int:
        """Оптимизированное массовое сохранение участников с пакетной обработкой"""
        if not members_data:
            return 0

        # Разбиваем на пакеты по 100 записей
        batch_size = 100
        total_saved = 0
        
        for i in range(0, len(members_data), batch_size):
            batch = members_data[i:i+batch_size]
            conn = sqlite3.connect(self.db_path)
            try:
                conn.executemany('''
                                 INSERT INTO members (user_id, username, display_name, found_method,
                                                      channel_name, first_seen, last_seen, discovery_count)
                                 VALUES (?, ?, ?, ?, ?, ?, ?, 1) ON CONFLICT(user_id) DO
                                 UPDATE SET
                                     last_seen = excluded.last_seen,
                                     discovery_count = discovery_count + 1,
                                     found_method = CASE
                                     WHEN found_method != excluded.found_method
                                     THEN found_method || ',' || excluded.found_method
                                     ELSE found_method
                                 END
                                 ''', batch)
                total_saved += conn.total_changes
                conn.commit()
                
                # Обновляем Bloom Filter после успешного сохранения
                if self.bloom_filter:
                    for member_data in batch:
                        member_id = member_data[0]  # user_id
                        self.bloom_filter.add(member_id)
            finally:
                conn.close()
        
        return total_saved

    async def save_members_async(self, members_data):
        """Асинхронное сохранение участников в отдельном потоке"""
        loop = asyncio.get_running_loop()
        await loop.run_in_executor(
            self.thread_pool, 
            self.save_members_batch, 
            members_data
        )

    def save_member_roles(self, user_id: int, roles: list):
        """Сохранение ролей участника в базу данных"""
        if not roles:
            return
            
        conn = sqlite3.connect(self.db_path)
        try:
            # Удаляем старые роли перед обновлением
            conn.execute("DELETE FROM member_roles WHERE user_id = ?", (user_id,))
            
            # Вставляем новые роли (исключаем @everyone роль)
            role_data = []
            for role in roles:
                if role.id != self.guild.id:  # Исключаем @everyone
                    role_data.append((
                        user_id, 
                        role.id, 
                        role.name, 
                        role.color.value if role.color else 0,
                        role.position
                    ))
            
            if role_data:
                conn.executemany('''
                    INSERT INTO member_roles (user_id, role_id, role_name, role_color, role_position)
                    VALUES (?, ?, ?, ?, ?)
                ''', role_data)
                conn.commit()
        except Exception as e:
            logger.error(f"❌ Ошибка сохранения ролей для {user_id}: {e}")
        finally:
            conn.close()

    def get_role_cache(self) -> dict:
        """Получение кэша ролей для оптимизации"""
        if not hasattr(self, '_role_cache'):
            self._role_cache = {}
        return self._role_cache

    async def scan_pinned_messages(self) -> SourceStats:
        """УЛУЧШЕННОЕ сканирование закрепленных сообщений с приоритизацией и rate limiting"""
        stats = SourceStats("pinned_messages")
        stats.start()

        logger.info("📌 УЛУЧШЕННОЕ СКАНИРОВАНИЕ ЗАКРЕПЛЕННЫХ СООБЩЕНИЙ")
        logger.info("   🔧 Добавлена приоритизация каналов и улучшенный rate limiting")

        members_data = []

        # Приоритизация каналов по активности и важности
        channels_with_priority = []
        for channel in self.guild.text_channels:
            if not self.has_permission(channel, 'read_message_history'):
                continue

            # Вычисляем приоритет канала
            priority = 0
            name_lower = channel.name.lower()

            # Высокий приоритет для важных каналов
            if any(keyword in name_lower for keyword in ['general', 'main', 'chat', 'welcome', 'announcements']):
                priority += 10

            # Средний приоритет для активных каналов
            if channel.last_message_id:
                priority += 5

            # Бонус за количество участников с доступом
            if hasattr(channel, 'members'):
                priority += min(len(channel.members) // 100, 3)  # Максимум +3

            channels_with_priority.append((priority, channel))

        # Сортируем по приоритету и ограничиваем количество
        channels_with_priority.sort(key=lambda x: x[0], reverse=True)
        priority_channels = [ch for _, ch in channels_with_priority[:50]]  # Топ-50 каналов

        logger.info(f"   📊 Приоритетных каналов для сканирования: {len(priority_channels)}")

        processed_pins = 0
        for channel in priority_channels:
            try:
                # Добавляем rate limiting для каждого канала
                await self.rate_limiter.wait_if_needed('pinned_scan')

                pins = await channel.pins()
                stats.add_processed(len(pins))
                processed_pins += len(pins)

                # Ограничиваем количество обрабатываемых закрепленных сообщений
                pins_to_process = pins[:100]  # Максимум 100 закрепленных сообщений на канал

                for message in pins_to_process:
                    # Обработка автора сообщения
                    if message.author and not message.author.bot:
                        if not self.is_member_found(message.author.id):
                            now = datetime.now().isoformat()
                            members_data.append((
                                message.author.id,
                                str(message.author),
                                getattr(message.author, 'display_name', str(message.author)),
                                "pinned_messages_author",
                                channel.name,
                                now,
                                now
                            ))
                            await self.mark_member_found(message.author.id)
                            stats.add_member()

                            # Сохраняем роли
                            if hasattr(message.author, 'roles'):
                                self.save_member_roles(message.author.id, message.author.roles)

                    # Обработка упоминаний в сообщении
                    for mention in message.mentions:
                        if not mention.bot and not self.is_member_found(mention.id):
                            now = datetime.now().isoformat()
                            members_data.append((
                                mention.id,
                                str(mention),
                                getattr(mention, 'display_name', str(mention)),
                                "pinned_messages_mention",
                                channel.name,
                                now,
                                now
                            ))
                            await self.mark_member_found(mention.id)
                            stats.add_member()

                            # Сохраняем роли
                            if hasattr(mention, 'roles'):
                                self.save_member_roles(mention.id, mention.roles)

                    # Обработка реакций на закрепленные сообщения (дополнительный источник)
                    if message.reactions:
                        for reaction in message.reactions[:5]:  # Максимум 5 реакций на сообщение
                            try:
                                # Ограничиваем количество пользователей реакции
                                user_count = 0
                                async for user in reaction.users():
                                    if user_count >= 10:  # Максимум 10 пользователей на реакцию
                                        break

                                    if not user.bot and not self.is_member_found(user.id):
                                        now = datetime.now().isoformat()
                                        members_data.append((
                                            user.id,
                                            str(user),
                                            getattr(user, 'display_name', str(user)),
                                            "pinned_reaction_user",
                                            f"{channel.name}_{reaction.emoji}",
                                            now,
                                            now
                                        ))
                                        await self.mark_member_found(user.id)
                                        stats.add_member()

                                        # Сохраняем роли
                                        if hasattr(user, 'roles'):
                                            self.save_member_roles(user.id, user.roles)

                                    user_count += 1
                            except Exception as e:
                                logger.debug(f"Ошибка обработки реакции в {channel.name}: {e}")
                                break

                # Логируем прогресс каждые 10 каналов
                if len([ch for ch in priority_channels if priority_channels.index(ch) <= priority_channels.index(channel)]) % 10 == 0:
                    logger.info(f"   📌 Обработано каналов: {priority_channels.index(channel) + 1}/{len(priority_channels)}")

            except Exception as e:
                logger.error(f"❌ Ошибка сканирования закрепленных сообщений в {channel.name}: {e}")
                stats.add_error()

        # Сохранение результатов
        if members_data:
            await self.save_members_async(members_data)

        stats.finish()
        logger.info(f"✅ УЛУЧШЕННЫЕ Закрепленные сообщения: {stats.members_found} участников за {stats.time_taken:.2f}с ({stats.efficiency:.1f} уч/с)")
        logger.info(f"   📊 Обработано закрепленных сообщений: {processed_pins}")
        logger.info(f"   📊 Приоритетных каналов: {len(priority_channels)}")

        self.source_stats['pinned_messages'] = stats
        self.save_source_stats(stats)
        return stats

    async def scan_integrations(self) -> SourceStats:
        """Сканирование интеграций сервера"""
        stats = SourceStats("integrations")
        stats.start()
        
        members_data = []
        
        try:
            integrations = await self.guild.integrations()
            stats.add_processed(len(integrations))
            
            for integration in integrations:
                if integration.user and not integration.user.bot:
                    if integration.user.id not in self.found_members:
                        now = datetime.now().isoformat()
                        members_data.append((
                            integration.user.id,
                            str(integration.user),
                            getattr(integration.user, 'display_name', str(integration.user)),
                            "integrations",
                            "server_integrations",
                            now,
                            now
                        ))
                        self.found_members.add(integration.user.id)
                        stats.add_member()
                        
                        # Сохраняем роли
                        if hasattr(integration.user, 'roles'):
                            self.save_member_roles(integration.user.id, integration.user.roles)
                            
        except discord.Forbidden:
            logger.warning("⚠️ Нет прав для просмотра интеграций")
        except Exception as e:
            logger.error(f"❌ Ошибка сканирования интеграций: {e}")
            stats.add_error()
        
        # Сохранение результатов
        if members_data:
            await self.save_members_async(members_data)
        
        stats.finish()
        logger.info(f"🔗 Интеграции: {stats.members_found} участников за {stats.time_taken:.2f}с ({stats.efficiency:.1f} уч/с)")
        
        self.source_stats['integrations'] = stats
        self.save_source_stats(stats)
        return stats

    async def scan_audit_log(self) -> SourceStats:
        """Сканирование лога аудита для поиска участников"""
        stats = SourceStats("audit_log")
        stats.start()

        logger.info("📋 СКАНИРОВАНИЕ ЛОГА АУДИТА")

        # Проверка прав доступа к логу аудита
        try:
            if not self.guild.me.guild_permissions.view_audit_log:
                logger.warning("⚠️ Нет прав для просмотра лога аудита")
                stats.finish()
                return stats
        except Exception as e:
            logger.warning(f"⚠️ Ошибка проверки прав доступа к логу аудита: {e}")
            stats.finish()
            return stats

        members_data = []

        try:
            await self.rate_limiter.wait_if_needed('audit_log_scan')

            async for entry in self.guild.audit_log(limit=100):
                stats.add_processed()

                # Пользователь, который выполнил действие
                if entry.user and not entry.user.bot:
                    if entry.user.id not in self.found_members:
                        now = datetime.now().isoformat()
                        members_data.append((
                            entry.user.id,
                            str(entry.user),
                            getattr(entry.user, 'display_name', str(entry.user)),
                            "audit_log_user",
                            f"action_{entry.action}",
                            now,
                            now
                        ))
                        await self.mark_member_found(entry.user.id)
                        stats.add_member()

                # Целевой пользователь (если есть)
                if hasattr(entry, 'target') and entry.target and not entry.target.bot:
                    if entry.target.id not in self.found_members:
                        now = datetime.now().isoformat()
                        members_data.append((
                            entry.target.id,
                            str(entry.target),
                            getattr(entry.target, 'display_name', str(entry.target)),
                            "audit_log_target",
                            f"action_{entry.action}",
                            now,
                            now
                        ))
                        await self.mark_member_found(entry.target.id)
                        stats.add_member()

        except discord.Forbidden:
            logger.warning("⚠️ Нет прав доступа к логу аудита")
            stats.add_error()
        except Exception as e:
            logger.error(f"❌ Ошибка сканирования лога аудита: {e}")
            stats.add_error()

        # Сохраняем найденных участников
        saved = self.save_members_batch(members_data)
        stats.finish()

        logger.info(f"✅ Audit Log: {stats.members_found} участников за {stats.time_taken:.2f}с "
                    f"({stats.efficiency:.1f} уч/с)")

        self.source_stats['audit_log'] = stats
        self.save_source_stats(stats)
        return stats

    async def scan_activities(self) -> SourceStats:
        """ИСПРАВЛЕННОЕ сканирование активностей участников - теперь находит НОВЫХ участников"""
        stats = SourceStats("activities")
        stats.start()

        logger.info("🎮 ИСПРАВЛЕННОЕ СКАНИРОВАНИЕ АКТИВНОСТЕЙ")
        logger.info("   🔧 Теперь сканирует ВСЕ источники активности, а не только кэш")

        members_data = []
        unique_members = set()

        try:
            # ИСПРАВЛЕНИЕ: Используем fetch_members для получения ВСЕХ участников с активностями
            await self.rate_limiter.wait_if_needed('memberlist_scan')

            # Метод 1: Сканирование через присутствие в голосовых каналах (активность)
            for voice_channel in self.guild.voice_channels:
                for member in voice_channel.members:
                    if (not member.bot and member.id not in unique_members and
                        not self.is_member_found(member.id)):
                        unique_members.add(member.id)
                        now = datetime.now().isoformat()
                        activity_info = "voice_activity"
                        if member.voice and member.voice.self_stream:
                            activity_info = "streaming_activity"
                        elif member.voice and member.voice.self_video:
                            activity_info = "video_activity"

                        members_data.append((
                            member.id,
                            str(member),
                            getattr(member, 'display_name', str(member)),
                            "voice_activities",
                            activity_info,
                            now,
                            now
                        ))
                        await self.mark_member_found(member.id)
                        stats.add_member()

                        # Сохраняем роли
                        if hasattr(member, 'roles'):
                            self.save_member_roles(member.id, member.roles)

            # Метод 2: Сканирование кэшированных участников с активностями
            for member in self.guild.members:
                if (member.activities and not member.bot and
                    member.id not in unique_members and not self.is_member_found(member.id)):
                    unique_members.add(member.id)
                    now = datetime.now().isoformat()

                    # Анализируем тип активности
                    activity_types = []
                    for activity in member.activities:
                        if hasattr(activity, 'type'):
                            activity_types.append(str(activity.type))

                    activity_info = ",".join(activity_types) if activity_types else "unknown_activity"

                    members_data.append((
                        member.id,
                        str(member),
                        getattr(member, 'display_name', str(member)),
                        "cached_activities",
                        activity_info,
                        now,
                        now
                    ))
                    await self.mark_member_found(member.id)
                    stats.add_member()

                    # Сохраняем роли
                    if hasattr(member, 'roles'):
                        self.save_member_roles(member.id, member.roles)

            # Метод 3: Поиск активных участников через недавние сообщения (косвенная активность)
            active_channels = sorted(
                [ch for ch in self.guild.text_channels if self.has_permission(ch, 'read_message_history')],
                key=lambda x: x.last_message_id or 0,
                reverse=True
            )[:10]  # Топ-10 активных каналов

            for channel in active_channels:
                try:
                    await self.rate_limiter.wait_if_needed('message_scan')

                    # Ищем сообщения за последние 2 часа (признак активности)
                    cutoff_time = datetime.now(timezone.utc) - timedelta(hours=2)

                    async for message in channel.history(limit=100, after=cutoff_time):
                        if (message.author and not message.author.bot and
                            message.author.id not in unique_members and
                            not self.is_member_found(message.author.id)):
                            unique_members.add(message.author.id)
                            now = datetime.now().isoformat()

                            members_data.append((
                                message.author.id,
                                str(message.author),
                                getattr(message.author, 'display_name', str(message.author)),
                                "recent_activity",
                                f"active_in_{channel.name}",
                                now,
                                now
                            ))
                            await self.mark_member_found(message.author.id)
                            stats.add_member()

                            # Сохраняем роли
                            if hasattr(message.author, 'roles'):
                                self.save_member_roles(message.author.id, message.author.roles)

                except Exception as e:
                    logger.debug(f"Ошибка сканирования активности в {channel.name}: {e}")
                    stats.add_error()

        except Exception as e:
            logger.error(f"❌ Ошибка сканирования активностей: {e}")
            stats.add_error()

        stats.add_processed(len(unique_members))

        # Сохранение результатов
        if members_data:
            await self.save_members_async(members_data)

        stats.finish()
        logger.info(f"✅ ИСПРАВЛЕННЫЕ Активности: {stats.members_found} участников за {stats.time_taken:.2f}с ({stats.efficiency:.1f} уч/с)")
        logger.info(f"   📊 Уникальных участников обработано: {len(unique_members)}")

        self.source_stats['activities'] = stats
        self.save_source_stats(stats)
        return stats

    async def scan_application_commands(self) -> SourceStats:
        """ПОЛНОСТЬЮ ПЕРЕРАБОТАННОЕ сканирование слэш-команд через мониторинг использования"""
        stats = SourceStats("application_commands")
        stats.start()

        logger.info("⚡ ПЕРЕРАБОТАННОЕ СКАНИРОВАНИЕ КОМАНД ПРИЛОЖЕНИЙ")
        logger.info("   🔧 Новый подход: мониторинг использования команд в реальном времени")

        members_data = []

        try:
            # НОВЫЙ ПОДХОД: Сканируем сообщения на предмет использования слэш-команд
            command_channels = [ch for ch in self.guild.text_channels
                              if self.has_permission(ch, 'read_message_history')]

            # Приоритет каналам с высокой активностью команд
            command_channels = sorted(command_channels,
                                    key=lambda x: x.last_message_id or 0,
                                    reverse=True)[:15]  # Топ-15 каналов

            command_users = set()

            for channel in command_channels:
                try:
                    await self.rate_limiter.wait_if_needed('message_scan')

                    # Ищем сообщения с взаимодействиями (interaction responses)
                    async for message in channel.history(limit=200):
                        stats.add_processed()

                        # Метод 1: Поиск ответов на слэш-команды
                        if (message.interaction and message.interaction.user and
                            not message.interaction.user.bot):
                            user = message.interaction.user
                            if user.id not in command_users and not self.is_member_found(user.id):
                                command_users.add(user.id)
                                now = datetime.now().isoformat()

                                command_name = getattr(message.interaction, 'name', 'unknown_command')
                                members_data.append((
                                    user.id,
                                    str(user),
                                    getattr(user, 'display_name', str(user)),
                                    "slash_command_user",
                                    f"cmd_{command_name}",
                                    now,
                                    now
                                ))
                                await self.mark_member_found(user.id)
                                stats.add_member()

                                # Сохраняем роли
                                if hasattr(user, 'roles'):
                                    self.save_member_roles(user.id, user.roles)

                        # Метод 2: Поиск сообщений, содержащих упоминания команд
                        if message.content and '/' in message.content and message.author:
                            # Ищем паттерны типа "/команда" в сообщениях
                            import re
                            command_pattern = r'/\w+'
                            if re.search(command_pattern, message.content) and not message.author.bot:
                                if (message.author.id not in command_users and
                                    not self.is_member_found(message.author.id)):
                                    command_users.add(message.author.id)
                                    now = datetime.now().isoformat()

                                    members_data.append((
                                        message.author.id,
                                        str(message.author),
                                        getattr(message.author, 'display_name', str(message.author)),
                                        "command_mention_user",
                                        channel.name,
                                        now,
                                        now
                                    ))
                                    await self.mark_member_found(message.author.id)
                                    stats.add_member()

                                    # Сохраняем роли
                                    if hasattr(message.author, 'roles'):
                                        self.save_member_roles(message.author.id, message.author.roles)

                        # Метод 3: Поиск пользователей через компоненты сообщений (кнопки, меню)
                        if message.components and message.author and not message.author.bot:
                            if (message.author.id not in command_users and
                                not self.is_member_found(message.author.id)):
                                command_users.add(message.author.id)
                                now = datetime.now().isoformat()

                                members_data.append((
                                    message.author.id,
                                    str(message.author),
                                    getattr(message.author, 'display_name', str(message.author)),
                                    "component_user",
                                    f"components_{channel.name}",
                                    now,
                                    now
                                ))
                                await self.mark_member_found(message.author.id)
                                stats.add_member()

                                # Сохраняем роли
                                if hasattr(message.author, 'roles'):
                                    self.save_member_roles(message.author.id, message.author.roles)

                except Exception as e:
                    logger.debug(f"Ошибка сканирования команд в {channel.name}: {e}")
                    stats.add_error()

            # ДОПОЛНИТЕЛЬНЫЙ МЕТОД: Сканирование через аудит лог (если доступен)
            try:
                if self.guild.me.guild_permissions.view_audit_log:
                    await self.rate_limiter.wait_if_needed('audit_log_scan')

                    # Ищем события связанные с командами
                    async for entry in self.guild.audit_logs(limit=100):
                        if (entry.user and not entry.user.bot and
                            entry.user.id not in command_users and
                            not self.is_member_found(entry.user.id)):

                            # Проверяем, связано ли событие с командами/интеграциями
                            if (hasattr(entry, 'extra') or
                                'application' in str(entry.action).lower() or
                                'integration' in str(entry.action).lower()):

                                command_users.add(entry.user.id)
                                now = datetime.now().isoformat()

                                members_data.append((
                                    entry.user.id,
                                    str(entry.user),
                                    getattr(entry.user, 'display_name', str(entry.user)),
                                    "audit_command_user",
                                    f"audit_{entry.action}",
                                    now,
                                    now
                                ))
                                await self.mark_member_found(entry.user.id)
                                stats.add_member()

                                # Сохраняем роли
                                if hasattr(entry.user, 'roles'):
                                    self.save_member_roles(entry.user.id, entry.user.roles)

            except discord.Forbidden:
                logger.debug("Нет доступа к аудит логу для команд")
            except Exception as e:
                logger.debug(f"Ошибка аудит лога команд: {e}")
                stats.add_error()

        except Exception as e:
            logger.error(f"❌ Ошибка переработанного сканирования команд: {e}")
            stats.add_error()

        # Сохранение результатов
        if members_data:
            await self.save_members_async(members_data)

        stats.finish()
        logger.info(f"✅ ПЕРЕРАБОТАННЫЕ Команды: {stats.members_found} участников за {stats.time_taken:.2f}с ({stats.efficiency:.1f} уч/с)")
        logger.info(f"   📊 Уникальных пользователей команд: {len(command_users)}")

        self.source_stats['application_commands'] = stats
        self.save_source_stats(stats)
        return stats

    async def analyze_activity_patterns(self) -> SourceStats:
        """Выявление участников по паттернам активности"""
        stats = SourceStats("activity_patterns")
        stats.start()
        
        # Добавляем общий таймаут для всего метода
        try:
            return await asyncio.wait_for(self._analyze_activity_patterns_internal(), timeout=30.0)
        except asyncio.TimeoutError:
            logger.warning("   ⏰ Таймаут анализа паттернов активности (30с)")
            stats.finish()
            return stats
        except Exception as e:
            logger.error(f"❌ Ошибка анализа паттернов активности: {e}")
            stats.finish()
            return stats
    
    async def _analyze_activity_patterns_internal(self) -> SourceStats:
        """Внутренняя реализация анализа паттернов активности"""
        stats = SourceStats("activity_patterns")
        stats.start()
        
        # Инициализируем переменную в начале
        members_data = []
        
        # Анализ временных меток сообщений
        active_hours = defaultdict(int)
        message_count = 0
        
        try:
            # Ограничиваем количество каналов для быстрого анализа
            channels_to_scan = self.guild.text_channels[:5]  # Только первые 5 каналов
            
            for channel in channels_to_scan:
                try:
                    # Ограничиваем количество сообщений для быстрого анализа
                    async for message in channel.history(limit=50):
                        if message.author and not message.author.bot:
                            hour = message.created_at.hour
                            active_hours[hour] += 1
                            message_count += 1
                            if message_count >= 250:  # Ограничиваем общее количество
                                break
                    if message_count >= 250:
                        break
                except Exception as e:
                    logger.debug(f"Ошибка анализа активности в {channel.name}: {e}")
                    stats.add_error()
            
            # Определение пиковых часов
            peak_hours = sorted(active_hours, key=active_hours.get, reverse=True)[:3]
            logger.info(f"   📊 Пиковые часы активности: {peak_hours}")
            
            # Если нет пиковых часов, используем стандартные часы активности
            if not peak_hours:
                peak_hours = [14, 15, 16, 17, 18, 19, 20, 21]  # Стандартные часы активности
                logger.info(f"   📊 Используем стандартные часы активности: {peak_hours}")
            
            # Поиск участников, активных в пиковые часы (ограниченный поиск)
            channels_for_search = self.guild.text_channels[:10]  # Ограничиваем поиск
            
            for channel in channels_for_search:
                try:
                    async for message in channel.history(limit=100):  # Уменьшаем лимит
                        if (message.author and not message.author.bot and 
                            message.created_at.hour in peak_hours and
                            not self.is_member_found(message.author.id)):
                            
                            now = datetime.now().isoformat()
                            members_data.append((
                                message.author.id,
                                str(message.author),
                                message.author.display_name,
                                "activity_pattern",
                                channel.name,
                                now,
                                now
                            ))
                            await self.mark_member_found(message.author.id)
                            stats.add_member()
                            
                            # Сохраняем роли
                            if hasattr(message.author, 'roles'):
                                self.save_member_roles(message.author.id, message.author.roles)
                            
                            # Ограничиваем количество найденных участников
                            if stats.members_found >= 50:
                                break
                    
                    if stats.members_found >= 50:
                        break
                
                except Exception as e:
                    logger.debug(f"Ошибка анализа активности в {channel.name}: {e}")
                    stats.add_error()
        
        except Exception as e:
            logger.error(f"❌ Ошибка анализа паттернов активности: {e}")
            stats.add_error()
        
        stats.add_processed(message_count)
        
        # Сохранение результатов
        if members_data:
            await self.save_members_async(members_data)
        
        stats.finish()
        logger.info(f"📈 Паттерны активности: {stats.members_found} участников за {stats.time_taken:.2f}с ({stats.efficiency:.1f} уч/с)")
        
        self.source_stats['activity_patterns'] = stats
        self.save_source_stats(stats)
        return stats

    async def batch_member_fetch(self, member_ids: List[int]) -> Dict[int, discord.Member]:
        """Пакетное получение информации об участниках"""
        results = {}
        batch_size = 50
        
        for i in range(0, len(member_ids), batch_size):
            batch = member_ids[i:i+batch_size]
            tasks = []
            
            for mid in batch:
                try:
                    task = self.guild.fetch_member(mid)
                    tasks.append(task)
                except Exception as e:
                    logger.debug(f"Ошибка создания задачи для {mid}: {e}")
            
            if tasks:
                try:
                    batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                    for j, result in enumerate(batch_results):
                        if isinstance(result, discord.Member):
                            results[batch[j]] = result
                        elif isinstance(result, Exception):
                            logger.debug(f"Ошибка получения участника {batch[j]}: {result}")
                except Exception as e:
                    logger.error(f"Ошибка пакетного запроса: {e}")
        
        return results

    def save_source_stats(self, stats: SourceStats):
        """Сохранение статистики источника"""
        conn = sqlite3.connect(self.db_path)
        try:
            conn.execute('''
                         INSERT INTO source_stats
                         (source_name, members_found, time_taken, items_processed, errors, efficiency, timestamp)
                         VALUES (?, ?, ?, ?, ?, ?, ?)
                         ''', (stats.name, stats.members_found, stats.time_taken,
                               stats.items_processed, stats.errors, stats.efficiency,
                               datetime.now().isoformat()))
            conn.commit()
        finally:
            conn.close()

    def save_realtime_discovery(self, user_id: int, username: str, channel_name: str,
                                message_content: str, discovery_type: str):
        """Сохранение обнаружения в реальном времени"""
        conn = sqlite3.connect(self.db_path)
        try:
            conn.execute('''
                         INSERT INTO realtime_discoveries
                         (user_id, username, channel_name, message_content, discovery_type, timestamp)
                         VALUES (?, ?, ?, ?, ?, ?)
                         ''', (user_id, username, channel_name, message_content[:200],
                               discovery_type, datetime.now().isoformat()))
            conn.commit()
        finally:
            conn.close()

    def get_member_count(self) -> int:
        """Получение общего количества участников"""
        conn = sqlite3.connect(self.db_path)
        try:
            cursor = conn.execute("SELECT COUNT(*) FROM members")
            return cursor.fetchone()[0]
        finally:
            conn.close()

    def get_problem_member_ids(self) -> set:
        """Получение ID проблемных участников для повторной проверки"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.execute("""
                SELECT user_id FROM members 
                WHERE discovery_count = 1 
                AND last_seen < datetime('now', '-1 hour')
                LIMIT 100
            """)
            problem_ids = {row[0] for row in cursor.fetchall()}
            conn.close()
            return problem_ids
        except Exception as e:
            logger.debug(f"Ошибка получения проблемных ID: {e}")
            return set()

    async def enhanced_cache_scan(self) -> SourceStats:
        """Расширенное сканирование кэша участников"""
        stats = SourceStats("cache_scan")
        stats.start()

        logger.info("🚀 РАСШИРЕННОЕ СКАНИРОВАНИЕ КЭША")

        members_data = []
        for member in self.guild.members:
            stats.add_processed()

            if member.id not in self.found_members and not member.bot:
                now = datetime.now().isoformat()
                members_data.append((
                    member.id,
                    str(member),
                    getattr(member, 'display_name', str(member)),
                    "cache_scan",
                    "guild_cache",
                    now,
                    now
                ))
                self.found_members.add(member.id)
                stats.add_member()

        saved = self.save_members_batch(members_data)
        stats.finish()

        logger.info(f"✅ Кэш: {stats.members_found} участников за {stats.time_taken:.2f}с "
                    f"({stats.efficiency:.0f} уч/с)")

        self.source_stats['cache_scan'] = stats
        self.save_source_stats(stats)
        return stats

    async def scan_threads_and_forums(self) -> SourceStats:
        """Сканирование тредов и форумных постов"""
        stats = SourceStats("threads_forums")
        stats.start()

        logger.info("🧵 СКАНИРОВАНИЕ ТРЕДОВ И ФОРУМОВ")

        members_data = []

        # Сканируем все каналы на предмет тредов
        for channel in self.guild.channels:
            if self.shutdown_event.is_set():
                break

            try:
                # Проверяем форумные каналы
                if hasattr(channel, 'type') and hasattr(discord.ChannelType, 'forum'):
                    if channel.type == discord.ChannelType.forum:
                        await self.rate_limiter.wait_if_needed('forum_scan')

                        logger.info(f"   📋 Форум: #{channel.name}")

                        # Получаем активные треды в форуме
                        async for thread in channel.archived_threads(limit=50):
                            stats.add_processed()

                            # Сканируем участников треда
                            for member in thread.members:
                                if member.id not in self.found_members and not member.bot:
                                    now = datetime.now().isoformat()
                                    members_data.append((
                                        member.id,
                                        str(member),
                                        getattr(member, 'display_name', str(member)),
                                        "forum_thread",
                                        f"{channel.name}/{thread.name}",
                                        now,
                                        now
                                    ))
                                    self.found_members.add(member.id)
                                    stats.add_member()

                # Проверяем текстовые каналы на треды
                elif hasattr(channel, 'threads'):
                    await self.rate_limiter.wait_if_needed('thread_scan')

                    # Активные треды
                    for thread in channel.threads:
                        stats.add_processed()

                        for member in thread.members:
                            if member.id not in self.found_members and not member.bot:
                                now = datetime.now().isoformat()
                                members_data.append((
                                    member.id,
                                    str(member),
                                    getattr(member, 'display_name', str(member)),
                                    "active_thread",
                                    f"{channel.name}/{thread.name}",
                                    now,
                                    now
                                ))
                                self.found_members.add(member.id)
                                stats.add_member()

                    # Архивированные треды
                    try:
                        async for thread in channel.archived_threads(limit=20):
                            stats.add_processed()

                            for member in thread.members:
                                if member.id not in self.found_members and not member.bot:
                                    now = datetime.now().isoformat()
                                    members_data.append((
                                        member.id,
                                        str(member),
                                        getattr(member, 'display_name', str(member)),
                                        "archived_thread",
                                        f"{channel.name}/{thread.name}",
                                        now,
                                        now
                                    ))
                                    self.found_members.add(member.id)
                                    stats.add_member()
                    except Exception as e:
                        logger.debug(f"Ошибка доступа к архивированным тредам: {e}")

            except Exception as e:
                stats.add_error()
                logger.debug(f"Ошибка сканирования {channel.name}: {e}")

        saved = self.save_members_batch(members_data)
        stats.finish()

        logger.info(f"✅ Треды/Форумы: {stats.members_found} участников за {stats.time_taken:.2f}с "
                    f"({stats.efficiency:.1f} уч/с)")

        self.source_stats['threads_forums'] = stats
        self.save_source_stats(stats)
        return stats

    async def optimized_reaction_scan(self, max_channels: int = 10, max_messages: int = 50) -> SourceStats:
        """Оптимизированное сканирование реакций с умным rate limiting"""
        stats = SourceStats("reaction_scan")
        stats.start()

        logger.info(f"👍 ОПТИМИЗИРОВАННОЕ СКАНИРОВАНИЕ РЕАКЦИЙ ({max_channels} каналов)")

        members_data = []

        # Получаем самые активные каналы
        text_channels = [ch for ch in self.guild.text_channels
                         if ch.permissions_for(self.guild.me).read_message_history]

        # Сортируем по активности
        active_channels = sorted(text_channels,
                                 key=lambda x: x.last_message_id or 0,
                                 reverse=True)[:max_channels]

        for channel in active_channels:
            if self.shutdown_event.is_set():
                break

            try:
                logger.info(f"   👍 Реакции в #{channel.name}")
                channel_members = 0

                await self.rate_limiter.wait_if_needed('message_scan')

                async for message in channel.history(limit=max_messages):
                    stats.add_processed()

                    # Сканируем только сообщения с реакциями
                    if message.reactions:
                        await self.rate_limiter.wait_if_needed('reaction_scan')

                        for reaction in message.reactions:
                            try:
                                # Увеличенный лимит пользователей на реакцию
                                user_count = 0
                                async for user in reaction.users():
                                    if user_count >= 25:  # Увеличено с 10 до 25
                                        break

                                    if user.id not in self.found_members and not user.bot:
                                        now = datetime.now().isoformat()
                                        members_data.append((
                                            user.id,
                                            str(user),
                                            getattr(user, 'display_name', str(user)),
                                            "reaction_scan",
                                            channel.name,
                                            now,
                                            now
                                        ))
                                        self.found_members.add(user.id)
                                        stats.add_member()
                                        channel_members += 1

                                    user_count += 1

                            except Exception as e:
                                stats.add_error()
                                logger.debug(f"Ошибка реакции: {e}")
                                break  # Прерываем сканирование реакций при ошибке

                logger.info(f"      ✅ {channel_members} участников")

            except Exception as e:
                stats.add_error()
                logger.debug(f"Ошибка сканирования реакций в {channel.name}: {e}")

        saved = self.save_members_batch(members_data)
        stats.finish()

        logger.info(f"✅ Реакции: {stats.members_found} участников за {stats.time_taken:.2f}с "
                    f"({stats.efficiency:.1f} уч/с)")

        self.source_stats['reaction_scan'] = stats
        self.save_source_stats(stats)
        return stats

    async def scan_system_messages(self, max_channels: int = 30) -> SourceStats:
        """Сканирование системных сообщений (присоединения, покидания)"""
        stats = SourceStats("system_messages")
        stats.start()

        logger.info(f"🔔 СКАНИРОВАНИЕ СИСТЕМНЫХ СООБЩЕНИЙ ({max_channels} каналов)")

        members_data = []

        # Получаем каналы с системными сообщениями
        system_channels = []
        for channel in self.guild.text_channels:
            if (channel.permissions_for(self.guild.me).read_message_history and
                    ('welcome' in channel.name.lower() or 'general' in channel.name.lower() or
                     'chat' in channel.name.lower() or channel == self.guild.system_channel)):
                system_channels.append(channel)

        system_channels = system_channels[:max_channels]

        for channel in system_channels:
            if self.shutdown_event.is_set():
                break

            try:
                logger.info(f"   🔔 Системные сообщения в #{channel.name}")
                channel_members = 0

                await self.rate_limiter.wait_if_needed('message_scan')

                async for message in channel.history(limit=200):
                    stats.add_processed()

                    # Расширенные ключевые слова для поиска
                    keywords = [
                        'joined', 'welcome', 'присоединился', 'добро пожаловать',
                        'привет', 'новый участник', 'рекомендовал', 'invite',
                        'добавлен', 'зашел', 'вошел', 'hello', 'hi there',
                        'new member', 'just joined', 'welcomed',
                        'добро пожаловать', 'присоединилась', 'новичок', 'новенький',
                        'приветствуем', 'добро пожаловать в', 'welcome to',
                        'just joined the server', 'new here', 'first time',
                        'присоединился к серверу', 'добро пожаловать в сервер'
                    ]

                    content_lower = message.content.lower()
                    has_keywords = any(keyword in content_lower for keyword in keywords)

                    # Ищем системные сообщения о присоединении
                    if (message.type == discord.MessageType.new_member or
                            message.type == discord.MessageType.premium_guild_subscription or
                            has_keywords):

                        # Автор системного сообщения
                        if (message.author.id not in self.found_members and
                                not message.author.bot):
                            now = datetime.now().isoformat()
                            members_data.append((
                                message.author.id,
                                str(message.author),
                                getattr(message.author, 'display_name', str(message.author)),
                                "system_message",
                                channel.name,
                                now,
                                now
                            ))
                            self.found_members.add(message.author.id)
                            stats.add_member()
                            channel_members += 1

                        # Упоминания в системных сообщениях
                        for mentioned in message.mentions:
                            if mentioned.id not in self.found_members and not mentioned.bot:
                                now = datetime.now().isoformat()
                                members_data.append((
                                    mentioned.id,
                                    str(mentioned),
                                    getattr(mentioned, 'display_name', str(mentioned)),
                                    "system_mention",
                                    channel.name,
                                    now,
                                    now
                                ))
                                self.found_members.add(mentioned.id)
                                stats.add_member()
                                channel_members += 1

                logger.info(f"      ✅ {channel_members} участников")

            except Exception as e:
                stats.add_error()
                logger.debug(f"Ошибка сканирования системных сообщений в {channel.name}: {e}")

        saved = self.save_members_batch(members_data)
        stats.finish()

        logger.info(f"✅ Системные сообщения: {stats.members_found} участников за {stats.time_taken:.2f}с "
                    f"({stats.efficiency:.1f} уч/с)")

        self.source_stats['system_messages'] = stats
        self.save_source_stats(stats)
        return stats

    async def scan_invites_and_roles(self) -> SourceStats:
        """Сканирование приглашений и ролей"""
        stats = SourceStats("invites_roles")
        stats.start()

        logger.info("🎫 СКАНИРОВАНИЕ ПРИГЛАШЕНИЙ И РОЛЕЙ")

        members_data = []

        try:
            # Сканируем приглашения (если есть права)
            await self.rate_limiter.wait_if_needed('invite_scan')

            try:
                invites = await self.guild.invites()
                for invite in invites:
                    stats.add_processed()

                    if (invite.inviter and
                            invite.inviter.id not in self.found_members and
                            not invite.inviter.bot):
                        now = datetime.now().isoformat()
                        members_data.append((
                            invite.inviter.id,
                            str(invite.inviter),
                            getattr(invite.inviter, 'display_name', str(invite.inviter)),
                            "invite_creator",
                            f"invite_{invite.code}",
                            now,
                            now
                        ))
                        self.found_members.add(invite.inviter.id)
                        stats.add_member()

                logger.info(f"   🎫 Обработано {len(invites)} приглашений")

            except discord.Forbidden:
                logger.info("   ❌ Нет прав для просмотра приглашений")

            # Сканируем участников через роли
            for role in self.guild.roles:
                if self.shutdown_event.is_set():
                    break

                stats.add_processed()

                # Пропускаем системные роли
                if role.is_default() or role.managed:
                    continue

                for member in role.members:
                    if member.id not in self.found_members and not member.bot:
                        now = datetime.now().isoformat()
                        members_data.append((
                            member.id,
                            str(member),
                            getattr(member, 'display_name', str(member)),
                            "role_member",
                            role.name,
                            now,
                            now
                        ))
                        self.found_members.add(member.id)
                        stats.add_member()

            logger.info(f"   👑 Обработано {len(self.guild.roles)} ролей")

        except Exception as e:
            stats.add_error()
            logger.debug(f"Ошибка сканирования приглашений/ролей: {e}")

        saved = self.save_members_batch(members_data)
        stats.finish()

        logger.info(f"✅ Приглашения/Роли: {stats.members_found} участников за {stats.time_taken:.2f}с "
                    f"({stats.efficiency:.1f} уч/с)")

        self.source_stats['invites_roles'] = stats
        self.save_source_stats(stats)
        return stats

    async def scan_full_memberlist(self) -> SourceStats:
        """Сканирование полного списка участников сервера через Discord API"""
        stats = SourceStats("full_memberlist")
        stats.start()

        logger.info("👥 СКАНИРОВАНИЕ ПОЛНОГО СПИСКА УЧАСТНИКОВ")
        logger.info(f"   📊 Начало сканирования: {len(self.found_members)} уже найдено")

        members_data = []
        unique_members = set()  # Для отслеживания уникальных участников

        try:
            logger.info("   🔍 Попытка получения полного списка...")
            await self.rate_limiter.wait_if_needed('memberlist_scan')

            # Метод 1: Кэшированные участники
            try:
                cached_members = list(self.guild.members)
                logger.info(f"   📊 Кэшированных участников: {len(cached_members)}")

                for member in cached_members:
                    stats.add_processed()
                    if not member.bot and member.id not in unique_members:  # ТОЛЬКО уникальные настоящие участники
                        unique_members.add(member.id)
                        now = datetime.now().isoformat()
                        status = getattr(member, 'status', 'unknown')
                        members_data.append((
                            member.id,
                            str(member),
                            getattr(member, 'display_name', str(member)),
                            "cached_memberlist",
                            f"status_{status}",
                            now,
                            now
                        ))
                        # Добавляем в статистику только уникальных
                        stats.add_member()
                        
                        # Помечаем как найденного
                        await self.mark_member_found(member.id)
                        
                        # Сохраняем роли участника
                        if hasattr(member, 'roles'):
                            self.save_member_roles(member.id, member.roles)

            except Exception as e:
                logger.error(f"   ❌ Ошибка кэшированных участников: {e}")
                stats.add_error()

            # Метод 2: ИСПРАВЛЕННЫЙ API участники (основной метод)
            try:
                logger.info("   🔍 ИСПРАВЛЕННОЕ получение участников через API...")
                api_members = []

                try:
                    # ИСПРАВЛЕНИЕ: Используем правильный метод для discord.py-self
                    # guild.fetch_members() не существует, используем chunk()
                    logger.info("   🔧 Используем guild.chunk() вместо несуществующего fetch_members()")

                    # Принудительно загружаем всех участников в кэш
                    await self.guild.chunk(cache=True)
                    api_members = list(self.guild.members)
                    logger.info(f"   📊 API участников получено через chunk(): {len(api_members)}")

                except AttributeError as e:
                    logger.info(f"   ⚠️ guild.chunk() недоступен: {e}")
                    # Альтернативный метод: используем enhanced_api_member_fetch
                    try:
                        api_members = await self.enhanced_api_member_fetch()
                        logger.info(f"   📊 API участников через enhanced_fetch: {len(api_members)}")
                    except Exception as e2:
                        logger.info(f"   ⚠️ Enhanced API метод тоже недоступен: {e2}")
                        api_members = []

                except Exception as e:
                    logger.info(f"   ⚠️ API метод недоступен: {e}")
                    # Если основной API не работает, пробуем альтернативные методы
                    try:
                        # Альтернатива 1: Получение через активные каналы
                        logger.info("   🔄 Пробуем альтернативный метод через активные каналы...")
                        api_members = set()

                        active_channels = sorted(
                            [ch for ch in self.guild.text_channels if self.has_permission(ch, 'read_message_history')],
                            key=lambda x: x.last_message_id or 0,
                            reverse=True
                        )[:20]  # Топ-20 активных каналов

                        for channel in active_channels:
                            try:
                                await self.rate_limiter.wait_if_needed('message_scan')
                                async for message in channel.history(limit=100):
                                    if message.author and isinstance(message.author, discord.Member):
                                        api_members.add(message.author)
                            except Exception as e3:
                                logger.debug(f"Ошибка в канале {channel.name}: {e3}")

                        api_members = list(api_members)
                        logger.info(f"   📊 Альтернативный API метод: {len(api_members)} участников")

                    except Exception as e2:
                        logger.info(f"   ⚠️ Альтернативный API метод тоже недоступен: {e2}")
                        api_members = []

                # Обрабатываем API участников
                for member in api_members:
                    stats.add_processed()
                    if not member.bot and member.id not in unique_members:  # ТОЛЬКО уникальные настоящие участники
                        unique_members.add(member.id)
                        now = datetime.now().isoformat()
                        status = getattr(member, 'status', 'unknown')
                        members_data.append((
                            member.id,
                            str(member),
                            getattr(member, 'display_name', str(member)),
                            "fixed_api_memberlist",  # Обновленное имя метода
                            f"status_{status}",
                            now,
                            now
                        ))
                        # Добавляем в статистику только уникальных
                        stats.add_member()

                        # Помечаем как найденного
                        await self.mark_member_found(member.id)

                        # Сохраняем роли участника
                        if hasattr(member, 'roles'):
                            self.save_member_roles(member.id, member.roles)

            except Exception as e:
                logger.debug(f"   ⚠️ Ошибка ИСПРАВЛЕННОГО API метода: {e}")
                stats.add_error()

            # Метод 3: Участники через роли
            try:
                logger.info("   🔍 Получение участников через роли...")
                role_members = set()
                
                for role in self.guild.roles:
                    if role.members:
                        for member in role.members:
                            if not member.bot and member.id not in unique_members:  # ТОЛЬКО уникальные настоящие участники
                                unique_members.add(member.id)
                                now = datetime.now().isoformat()
                                status = getattr(member, 'status', 'unknown')
                                members_data.append((
                                    member.id,
                                    str(member),
                                    getattr(member, 'display_name', str(member)),
                                    "role_memberlist",
                                    f"role_{role.name}",
                                    now,
                                    now
                                ))
                                # Добавляем в статистику только уникальных
                                stats.add_member()
                                
                                # Помечаем как найденного
                                await self.mark_member_found(member.id)
                                
                                # Сохраняем роли участника
                                if hasattr(member, 'roles'):
                                    self.save_member_roles(member.id, member.roles)
                                role_members.add(member.id)

                logger.info(f"   📊 Участников через роли: {len(role_members)}")

            except Exception as e:
                logger.debug(f"   ⚠️ Ошибка ролей: {e}")

            # Метод 4: Участники в голосовых каналах
            try:
                logger.info("   🔍 Получение участников в голосовых каналах...")
                voice_members = set()
                
                for voice_channel in self.guild.voice_channels:
                    for member in voice_channel.members:
                        if not member.bot and member.id not in unique_members:  # ТОЛЬКО уникальные настоящие участники
                            unique_members.add(member.id)
                            now = datetime.now().isoformat()
                            status = getattr(member, 'status', 'unknown')
                            members_data.append((
                                member.id,
                                str(member),
                                getattr(member, 'display_name', str(member)),
                                "voice_memberlist",
                                f"voice_{voice_channel.name}",
                                now,
                                now
                            ))
                            # Добавляем в статистику только уникальных
                            stats.add_member()
                            
                            # Помечаем как найденного
                            await self.mark_member_found(member.id)
                            
                            # Сохраняем роли участника
                            if hasattr(member, 'roles'):
                                self.save_member_roles(member.id, member.roles)
                            voice_members.add(member.id)

                logger.info(f"   📊 Участников в голосовых каналах: {len(voice_members)}")

            except Exception as e:
                logger.debug(f"   ⚠️ Ошибка голосовых каналов: {e}")

            # Метод 5: Участники через каналы (дополнительный метод)
            try:
                logger.info("   🔍 Получение участников через каналы...")
                channel_members = set()
                
                # Проверяем текстовые каналы на наличие участников
                for text_channel in self.guild.text_channels:
                    try:
                        # Попытка получить участников канала (если доступно)
                        if hasattr(text_channel, 'members'):
                            for member in text_channel.members:
                                if not member.bot and member.id not in unique_members:  # ТОЛЬКО уникальные настоящие участники
                                    unique_members.add(member.id)
                                    now = datetime.now().isoformat()
                                    status = getattr(member, 'status', 'unknown')
                                    members_data.append((
                                        member.id,
                                        str(member),
                                        getattr(member, 'display_name', str(member)),
                                        "channel_memberlist",
                                        f"channel_{text_channel.name}",
                                        now,
                                        now
                                    ))
                                    # Добавляем в статистику только уникальных
                                    stats.add_member()
                                    
                                    # Помечаем как найденного
                                    await self.mark_member_found(member.id)
                                    
                                    # Сохраняем роли участника
                                    if hasattr(member, 'roles'):
                                        self.save_member_roles(member.id, member.roles)
                                    channel_members.add(member.id)
                    except:
                        pass

                logger.info(f"   📊 Участников через каналы: {len(channel_members)}")

            except Exception as e:
                logger.debug(f"   ⚠️ Ошибка каналов: {e}")

        except Exception as e:
            logger.error(f"   ❌ Критическая ошибка: {type(e).__name__} - {e}")
            stats.add_error()

        # Сохраняем найденных участников
        saved = self.save_members_batch(members_data)
        stats.finish()
        
        # Фиксация реального количества уникальных участников
        logger.info(f"   📊 Уникальных участников найдено: {len(unique_members)}")
        logger.info(f"   📊 Участников сохранено в БД: {saved}")

        logger.info(f"✅ Полный список: {stats.members_found} участников за {stats.time_taken:.2f}с")
        logger.info(f"   📊 Memberlist scan: found {len(members_data)} members")
        self.source_stats['full_memberlist'] = stats
        self.save_source_stats(stats)
        return stats

    async def monitor_memberlist_changes(self) -> SourceStats:
        """Мониторинг изменений в списке участников сервера"""
        stats = SourceStats("memberlist_changes")
        stats.start()

        logger.info("🔄 МОНИТОРИНГ ИЗМЕНЕНИЙ СПИСКА УЧАСТНИКОВ")

        # Получаем предыдущий снимок участников из БД
        previous_members = set()
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.execute("SELECT user_id FROM members")
            previous_members = {row[0] for row in cursor.fetchall()}
            conn.close()
            logger.info(f"   📊 Предыдущий снимок: {len(previous_members)} участников")
        except Exception as e:
            logger.error(f"   ❌ Ошибка загрузки предыдущего снимка: {e}")
            stats.add_error()

        # Получаем текущий список участников
        current_members = {}  # {user_id: (member_obj, status)}
        members_data = []

        try:
            # Получаем текущий список участников
            try:
                logger.info("   🔍 Получение текущего списка участников...")
                # Используем кэшированных участников (более надежно)
                for member in self.guild.members:
                    stats.add_processed()
                    status = getattr(member, 'status', 'unknown')
                    current_members[member.id] = (member, status)

                logger.info(f"   📊 Текущий снимок: {len(current_members)} участников")

            except Exception as e:
                logger.error(f"   ❌ Ошибка получения участников: {e}")
                stats.add_error()

        except Exception as e:
            logger.error(f"   ❌ Ошибка получения текущего списка: {e}")
            stats.add_error()
            stats.finish()
            return stats

        # Анализируем изменения
        current_ids = set(current_members.keys())

        # Новые участники
        new_members = current_ids - previous_members
        # Покинувшие участники
        left_members = previous_members - current_ids

        logger.info(f"   🆕 Новых участников: {len(new_members)}")
        logger.info(f"   👋 Покинули сервер: {len(left_members)}")

        # Сохраняем новых участников
        for user_id in new_members:
            if user_id in current_members:
                member, status = current_members[user_id]

                if not member.bot:
                    now = datetime.now().isoformat()
                    members_data.append((
                        member.id,
                        str(member),
                        getattr(member, 'display_name', str(member)),
                        "memberlist_new",
                        f"joined_status_{status}",
                        now,
                        now
                    ))

                    if member.id not in self.found_members:
                        self.found_members.add(member.id)
                        stats.add_member()

                    # Логируем в реальном времени
                    self.save_realtime_discovery(
                        member.id,
                        str(member),
                        "memberlist_monitor",
                        f"Присоединился к серверу (статус: {status})",
                        "member_joined"
                    )

        # Логируем покинувших участников
        for user_id in left_members:
            try:
                # Пытаемся получить информацию о пользователе из БД
                conn = sqlite3.connect(self.db_path)
                cursor = conn.execute("SELECT username FROM members WHERE user_id = ?", (user_id,))
                result = cursor.fetchone()
                conn.close()

                if result:
                    username = result[0]
                    logger.info(f"   👋 Покинул сервер: {username} (ID: {user_id})")

                    # Сохраняем информацию о выходе
                    self.save_realtime_discovery(
                        user_id,
                        username,
                        "memberlist_monitor",
                        "Покинул сервер",
                        "member_left"
                    )

            except Exception as e:
                logger.debug(f"Ошибка обработки покинувшего участника {user_id}: {e}")

        # Сохраняем новых участников
        saved = self.save_members_batch(members_data)
        stats.finish()

        logger.info(f"✅ Мониторинг изменений: {stats.members_found} новых участников за {stats.time_taken:.2f}с")

        self.source_stats['memberlist_changes'] = stats
        self.save_source_stats(stats)
        return stats

    async def quick_memberlist_check(self) -> SourceStats:
        """Быстрая проверка новых участников через memberlist"""
        stats = SourceStats("quick_memberlist")
        stats.start()

        logger.info("⚡ БЫСТРАЯ ПРОВЕРКА MEMBERLIST")

        members_data = []

        try:
            # Используем кэшированных участников для быстрой проверки
            logger.info("   🔍 Быстрая проверка кэшированных участников...")
            
            # Получаем участников из кэша (это работает в discord.py-self)
            cached_members = list(self.guild.members)
            logger.info(f"   📊 Кэшированных участников: {len(cached_members)}")

            for member in cached_members:
                stats.add_processed()
                if member.id not in self.found_members and not member.bot:
                    now = datetime.now().isoformat()
                    status = getattr(member, 'status', 'unknown')
                    members_data.append((
                        member.id,
                        str(member),
                        getattr(member, 'display_name', str(member)),
                        "quick_memberlist",
                        f"status_{status}",
                        now,
                        now
                    ))
                    self.found_members.add(member.id)
                    stats.add_member()
                    logger.info(f"   🆕 НОВЫЙ: {member} (статус: {status})")

            logger.info(f"   ✅ Проверено: {len(cached_members)} участников")

        except Exception as e:
            logger.error(f"   ❌ Ошибка быстрой проверки: {e}")
            stats.add_error()

        # Сохраняем новых участников
        saved = self.save_members_batch(members_data)
        stats.finish()

        logger.info(f"✅ Быстрая проверка: {stats.members_found} новых участников")
        return stats

    async def scan_new_channels(self):
        """Сканирование новых каналов, которые могли появиться"""
        try:
            logger.info("🔍 ПРОВЕРКА НОВЫХ КАНАЛОВ")

            # Получаем текущие каналы
            current_channels = set(ch.id for ch in self.guild.text_channels)

            # Проверяем, есть ли новые каналы (простая проверка)
            if hasattr(self, '_last_channel_count'):
                if len(current_channels) > self._last_channel_count:
                    new_channels_count = len(current_channels) - self._last_channel_count
                    logger.info(f"   🆕 Обнаружено {new_channels_count} новых каналов!")

                    # Быстро сканируем новые каналы
                    new_channels = [
                                       ch for ch in self.guild.text_channels
                                       if ch.permissions_for(self.guild.me).read_message_history
                                   ][-new_channels_count:]  # Берем последние каналы

                    for channel in new_channels:
                        try:
                            logger.info(f"   📂 Сканирую новый канал #{channel.name}")

                            await self.rate_limiter.wait_if_needed('message_scan')

                            message_count = 0
                            async for message in channel.history(limit=50):
                                message_count += 1

                                # Авторы сообщений
                                if (message.author.id not in self.found_members and
                                        not message.author.bot):
                                    now = datetime.now().isoformat()
                                    member_data = [(
                                        message.author.id,
                                        str(message.author),
                                        getattr(message.author, 'display_name', str(message.author)),
                                        "new_channel_scan",
                                        channel.name,
                                        now,
                                        now
                                    )]

                                    self.save_members_batch(member_data)
                                    self.found_members.add(message.author.id)

                                    logger.info(f"      🆕 Новый участник: {message.author}")

                            logger.info(f"      ✅ Обработано {message_count} сообщений")

                        except Exception as e:
                            logger.debug(f"Ошибка сканирования нового канала {channel.name}: {e}")

            # Сохраняем текущее количество каналов
            self._last_channel_count = len(current_channels)

        except Exception as e:
            logger.error(f"Ошибка проверки новых каналов: {e}")

    async def continuous_memberlist_monitor(self):
        """ПЕРЕРАБОТАННЫЙ event-driven мониторинг вместо грубого сканирования"""
        logger.info("🔥 ПЕРЕРАБОТАННЫЙ EVENT-DRIVEN МОНИТОРИНГ ЗАПУЩЕН")
        logger.info("   🔧 Теперь использует события Discord вместо постоянного сканирования")
        logger.info("   ⚡ Значительно снижена нагрузка на API")

        continuous_discoveries = 0
        scan_count = 0
        last_scan_time = time.time()

        # НОВЫЕ адаптивные интервалы (значительно увеличены для снижения нагрузки)
        base_interval = 300  # Базовый интервал 5 минут (было 45 секунд)
        min_interval = 180   # Минимум 3 минуты (было 30 секунд)
        max_interval = 600   # Максимум 10 минут (было 90 секунд)
        current_interval = base_interval

        # Кэш для отслеживания изменений
        last_member_count = len(self.guild.members)
        last_member_snapshot = set(member.id for member in self.guild.members)

        while self.monitoring_active and not self.shutdown_event.is_set():
            try:
                scan_count += 1
                scan_start = time.time()

                logger.info(f"🔥 НЕПРЕРЫВНОЕ СКАНИРОВАНИЕ #{scan_count}")

                # НОВЫЙ ПОДХОД: Event-driven проверка изменений
                new_members_found = 0
                members_data = []

                try:
                    # Проверяем изменения вместо полного сканирования
                    current_members = set(member.id for member in self.guild.members)
                    current_member_count = len(self.guild.members)

                    # Проверяем, изменилось ли количество участников
                    if current_member_count != last_member_count:
                        logger.info(f"   📊 Изменение количества: {last_member_count} → {current_member_count}")

                        # Находим новых участников (эффективно через разность множеств)
                        new_member_ids = current_members - last_member_snapshot
                        left_member_ids = last_member_snapshot - current_members

                        if new_member_ids:
                            logger.info(f"   🆕 Обнаружено новых участников: {len(new_member_ids)}")

                            # Получаем объекты новых участников
                            for member_id in new_member_ids:
                                try:
                                    member = self.guild.get_member(member_id)
                                    if member and not member.bot and member.id not in self.found_members:
                                        now = datetime.now().isoformat()
                                        status = getattr(member, 'status', 'unknown')
                                        members_data.append((
                                            member.id,
                                            str(member),
                                            getattr(member, 'display_name', str(member)),
                                            "event_driven_monitor",  # Обновленное имя
                                            f"member_join_{status}",
                                            now,
                                            now
                                        ))
                                        self.found_members.add(member.id)
                                        new_members_found += 1
                                        logger.info(f"   🆕 НОВЫЙ: {member} (статус: {status})")

                                        # Сохраняем роли
                                        if hasattr(member, 'roles'):
                                            self.save_member_roles(member.id, member.roles)
                                except Exception as e:
                                    logger.debug(f"Ошибка обработки нового участника {member_id}: {e}")

                        if left_member_ids:
                            logger.info(f"   👋 Участников покинуло сервер: {len(left_member_ids)}")

                        # Обновляем снимок
                        last_member_snapshot = current_members.copy()
                        last_member_count = current_member_count

                    else:
                        # Если количество не изменилось, делаем легкую проверку
                        members = list(self.guild.members)
                        for member in members[-5:]:  # Проверяем только последних 5
                            if member.id not in self.found_members and not member.bot:
                                now = datetime.now().isoformat()
                                status = getattr(member, 'status', 'unknown')
                                members_data.append((
                                    member.id,
                                    str(member),
                                    getattr(member, 'display_name', str(member)),
                                    "light_check_monitor",
                                    f"light_check_{status}",
                                    now,
                                    now
                                ))
                                self.found_members.add(member.id)
                                new_members_found += 1
                                logger.info(f"   🔍 НАЙДЕН при легкой проверке: {member}")

                    # Сохраняем новых участников
                    if members_data:
                        self.save_members_batch(members_data)
                        continuous_discoveries += new_members_found

                    scan_time = time.time() - scan_start

                    # УЛУЧШЕННАЯ адаптивная настройка интервала
                    if new_members_found > 0:
                        # Если нашли участников, немного уменьшаем интервал
                        current_interval = max(min_interval, current_interval - 30)  # Уменьшаем на 30 сек
                        logger.info(
                            f"   🎯 Найдено {new_members_found} участников! Ускоряю проверки (интервал: {current_interval}с)")
                    else:
                        # Если не нашли, увеличиваем интервал
                        current_interval = min(max_interval, current_interval + 60)  # Увеличиваем на 1 мин

                    if scan_count % 5 == 0:  # Логируем каждые 5 проверок вместо каждой
                        logger.info(
                            f"   📊 Проверка #{scan_count}: {new_members_found} новых за {scan_time:.1f}с")
                        logger.info(f"   🔥 Всего найдено event-driven мониторингом: {continuous_discoveries}")

                except Exception as e:
                    logger.error(f"   ❌ Ошибка event-driven мониторинга: {e}")
                    # При ошибке значительно увеличиваем интервал
                    current_interval = min(max_interval, current_interval + 120)  # +2 минуты

                # Показываем статистику каждые 10 сканирований
                if scan_count % 10 == 0:
                    total_time = time.time() - last_scan_time
                    avg_time_per_scan = total_time / 10

                    logger.info(f"\n🔥 СТАТИСТИКА НЕПРЕРЫВНОГО МОНИТОРИНГА:")
                    logger.info(f"   📊 Выполнено сканирований: {scan_count}")
                    logger.info(f"   🆕 Найдено участников: {continuous_discoveries}")
                    logger.info(f"   ⚡ Среднее время сканирования: {avg_time_per_scan:.1f}с")
                    logger.info(f"   🔄 Текущий интервал: {current_interval}с")
                    logger.info(
                        f"   📈 Эффективность: {continuous_discoveries / scan_count:.2f} участников/сканирование")

                    last_scan_time = time.time()

                # Ждем до следующего сканирования
                await asyncio.sleep(current_interval)

            except Exception as e:
                logger.error(f"❌ Критическая ошибка непрерывного мониторинга: {e}")
                await asyncio.sleep(60)  # Пауза при критической ошибке

        logger.info(f"🔥 НЕПРЕРЫВНЫЙ МОНИТОРИНГ ЗАВЕРШЕН. Всего найдено: {continuous_discoveries} участников")

    async def enhanced_memberlist_scan(self) -> SourceStats:
        """Улучшенное сканирование member list с поддержкой discord.py-self и 5 независимыми методами"""
        stats = SourceStats("enhanced_memberlist")
        stats.start()

        logger.info("👥 УЛУЧШЕННОЕ СКАНИРОВАНИЕ MEMBER LIST (5 НЕЗАВИСИМЫХ МЕТОДОВ)")

        # Запускаем все 5 методов параллельно
        tasks = [
            self.scan_cached_members(),
            self.scan_role_members(),
            self.scan_voice_members(),
            self.scan_api_members(),
            self.scan_problem_members()
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Обработка результатов
        members_data = []
        total_found = 0
        successful_methods = 0
        
        for i, result in enumerate(results):
            method_names = ['кэш', 'роли', 'голос', 'API', 'проблемные']
            if isinstance(result, list):
                members_data.extend(result)
                total_found += len(result)
                successful_methods += 1
                logger.info(f"   ✅ Метод {method_names[i]}: {len(result)} участников")
            elif isinstance(result, Exception):
                logger.warning(f"   ⚠️ Метод {method_names[i]} не удался: {result}")
            else:
                logger.info(f"   ⚠️ Метод {method_names[i]}: неожиданный результат")

        # Сохраняем найденных участников
        saved = self.save_members_batch(members_data)
        stats.finish()

        logger.info(f"✅ Улучшенный member list: {total_found} участников за {stats.time_taken:.2f}с")
        logger.info(f"📊 Успешных методов: {successful_methods}/5")
        logger.info(f"⚡ Эффективность: {total_found / stats.time_taken:.1f} уч/с")
        logger.info(f"   📊 Enhanced memberlist scan: found {len(members_data)} members")
        
        self.source_stats['enhanced_memberlist'] = stats
        self.save_source_stats(stats)
        return stats

    async def scan_cached_members(self) -> List[tuple]:
        """Сканирование кэшированных участников"""
        members_data = []
        try:
            logger.info("   🔍 Метод 1: Кэшированные участники...")
            cached_members = list(self.guild.members)
            logger.info(f"   📊 Кэшированных участников: {len(cached_members)}")
            
            for member in cached_members:
                if not member.bot:
                    now = datetime.now().isoformat()
                    status = getattr(member, 'status', 'unknown')
                    members_data.append((
                        member.id,
                        str(member),
                        getattr(member, 'display_name', str(member)),
                        "cached_memberlist",
                        f"status_{status}",
                        now,
                        now
                    ))
                    # ВСЕГДА добавляем в статистику
                    await self.mark_member_found(member.id)
                    # Сохраняем роли участника
                    if hasattr(member, 'roles'):
                        self.save_member_roles(member.id, member.roles)
        except Exception as e:
            logger.debug(f"   ⚠️ Ошибка кэшированных участников: {e}")
        
        return members_data

    async def scan_role_members(self) -> List[tuple]:
        """Сканирование участников через роли"""
        members_data = []
        try:
            logger.info("   🔍 Метод 2: Участники через роли...")
            role_members = set()
            for role in self.guild.roles:
                if role.members:
                    for member in role.members:
                        if not member.bot:
                            now = datetime.now().isoformat()
                            status = getattr(member, 'status', 'unknown')
                            members_data.append((
                                member.id,
                                str(member),
                                getattr(member, 'display_name', str(member)),
                                "role_memberlist",
                                f"role_{role.name}",
                                now,
                                now
                            ))
                            # ВСЕГДА добавляем в статистику
                            await self.mark_member_found(member.id)
                            # Сохраняем роли участника
                            if hasattr(member, 'roles'):
                                self.save_member_roles(member.id, member.roles)
                            role_members.add(member.id)

            logger.info(f"   📊 Уникальных участников через роли: {len(role_members)}")
        except Exception as e:
            logger.debug(f"   ⚠️ Ошибка ролей: {e}")
        
        return members_data

    async def scan_voice_members(self) -> List[tuple]:
        """Сканирование участников в голосовых каналах"""
        members_data = []
        try:
            logger.info("   🔍 Метод 3: Участники в голосовых каналах...")
            voice_members = set()
            for voice_channel in self.guild.voice_channels:
                for member in voice_channel.members:
                    if not member.bot:
                        now = datetime.now().isoformat()
                        status = getattr(member, 'status', 'unknown')
                        members_data.append((
                            member.id,
                            str(member),
                            getattr(member, 'display_name', str(member)),
                            "voice_memberlist",
                            f"voice_{voice_channel.name}",
                            now,
                            now
                        ))
                        # ВСЕГДА добавляем в статистику
                        await self.mark_member_found(member.id)
                        voice_members.add(member.id)

            logger.info(f"   📊 Участников в голосовых каналах: {len(voice_members)}")
        except Exception as e:
            logger.debug(f"   ⚠️ Ошибка голосовых каналов: {e}")
        
        return members_data

    async def scan_api_members(self) -> List[tuple]:
        """Безопасное сканирование через API с улучшенной обработкой ошибок"""
        members_data = []
        try:
            logger.info("   🔍 Метод 4: Безопасная попытка API...")
            
            # Попытка получить участников через API с повторными попытками
            api_members = await self.safe_fetch_members()
            
            if api_members:
                logger.info(f"   📊 API участников получено: {len(api_members)}")
                
                # Обрабатываем API участников
                for member in api_members:
                    if not member.bot:
                        now = datetime.now().isoformat()
                        status = getattr(member, 'status', 'unknown')
                        members_data.append((
                            member.id,
                            str(member),
                            getattr(member, 'display_name', str(member)),
                            "api_memberlist",
                            f"status_{status}",
                            now,
                            now
                        ))
                        # ВСЕГДА добавляем в статистику
                        await self.mark_member_found(member.id)
            else:
                logger.info("   ⚠️ API участники не получены, пропускаем этот метод")

        except Exception as e:
            logger.debug(f"   ⚠️ Ошибка API метода: {e}")
            logger.info("   ⚠️ API метод недоступен, продолжаем с другими методами")
        
        return members_data

    async def safe_fetch_members(self):
        """Безопасное получение участников с улучшенной обработкой ошибок"""
        try:
            return await self.enhanced_api_member_fetch()
        except discord.HTTPException as e:
            logger.error(f"🚨 Ошибка HTTP {e.status}: {e.text}")
            if e.status == 429:
                retry_after = e.retry_after or 30
                logger.info(f"⏳ Ожидание {retry_after} секунд перед повторной попыткой")
                await asyncio.sleep(retry_after)
                return await self.safe_fetch_members()
        except Exception as e:
            logger.error(f"🚨 Необработанная ошибка: {type(e).__name__} - {e}")
            return []

    async def scan_problem_members(self) -> List[tuple]:
        """Поиск проблемных участников с улучшенной обработкой"""
        members_data = []
        try:
            logger.info("   🔍 Метод 5: Поиск проблемных участников...")
            problem_ids = self.get_problem_member_ids()
            
            if problem_ids:
                logger.info(f"   📊 Проблемных ID для проверки: {len(problem_ids)}")
                
                # Параллельная проверка проблемных участников
                tasks = []
                for user_id in problem_ids:
                    tasks.append(self.fetch_problem_member(user_id))
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for result in results:
                    if isinstance(result, tuple):
                        members_data.append(result)
                        if not self.is_member_found(result[0]):  # user_id
                            await self.mark_member_found(result[0])
                
                logger.info(f"   ✅ Найдено проблемных участников: {len(members_data)}")
            else:
                logger.info("   📊 Проблемных участников для проверки нет")
                
        except Exception as e:
            logger.debug(f"   ⚠️ Ошибка проблемных участников: {e}")
        
        return members_data

    async def fetch_problem_member(self, user_id: int) -> Optional[tuple]:
        """Получение отдельного проблемного участника"""
        try:
            member = await self.guild.fetch_member(user_id)
            if not member.bot:
                now = datetime.now().isoformat()
                status = getattr(member, 'status', 'unknown')
                return (
                    member.id,
                    str(member),
                    getattr(member, 'display_name', str(member)),
                    "problem_memberlist",
                    f"status_{status}",
                    now,
                    now
                )
        except Exception as e:
            logger.debug(f"   ⚠️ Не удалось получить участника {user_id}: {e}")
        
        return None

    async def scan_events(self) -> SourceStats:
        """Сканирование событий сервера (scheduled events)"""
        stats = SourceStats("events_scan")
        stats.start()

        logger.info("🎉 СКАНИРОВАНИЕ СОБЫТИЙ СЕРВЕРА")

        members_data = []

        try:
            # Получаем запланированные события
            events = await self.guild.fetch_scheduled_events()
            logger.info(f"   📅 Найдено событий: {len(events)}")

            for event in events:
                stats.add_processed()

                try:
                    # Создатель события
                    if (event.creator and
                            event.creator.id not in self.found_members and
                            not event.creator.bot):
                        now = datetime.now().isoformat()
                        members_data.append((
                            event.creator.id,
                            str(event.creator),
                            getattr(event.creator, 'display_name', str(event.creator)),
                            "event_creator",
                            f"event_{event.name}",
                            now,
                            now
                        ))
                        self.found_members.add(event.creator.id)
                        stats.add_member()

                        logger.info(f"   🆕 Создатель события: {event.creator}")

                    # Участники события
                    try:
                        async for user in event.users():
                            if user.id not in self.found_members and not user.bot:
                                now = datetime.now().isoformat()
                                members_data.append((
                                    user.id,
                                    str(user),
                                    getattr(user, 'display_name', str(user)),
                                    "event_participant",
                                    f"event_{event.name}",
                                    now,
                                    now
                                ))
                                self.found_members.add(user.id)
                                stats.add_member()

                        logger.info(f"   📅 Событие '{event.name}': найдено участников")

                    except Exception as e:
                        logger.debug(f"Ошибка получения участников события {event.name}: {e}")

                except Exception as e:
                    stats.add_error()
                    logger.debug(f"Ошибка обработки события: {e}")

        except discord.Forbidden:
            logger.info("   ❌ Нет прав для просмотра событий")
        except Exception as e:
            logger.error(f"   ❌ Ошибка сканирования событий: {e}")
            stats.add_error()

        # Сохраняем найденных участников
        saved = self.save_members_batch(members_data)
        stats.finish()

        logger.info(f"✅ События: {stats.members_found} участников за {stats.time_taken:.2f}с "
                    f"({stats.efficiency:.1f} уч/с)")

        self.source_stats['events_scan'] = stats
        self.save_source_stats(stats)
        return stats

    async def scan_embeds(self) -> SourceStats:
        """Сканирование встроенного контента (embeds)"""
        stats = SourceStats("embeds_scan")
        stats.start()

        try:
            logger.info("🔗 СКАНИРОВАНИЕ ВСТРОЕННОГО КОНТЕНТА")

            members_data = []

            # Получаем каналы с приоритизацией
            text_channels = [ch for ch in self.guild.text_channels
                             if ch.permissions_for(self.guild.me).read_message_history]
            prioritized_channels = self.prioritize_channels(text_channels)[:10]  # Топ-10 каналов

            for channel, score in prioritized_channels:
                if self.shutdown_event.is_set():
                    break

                try:
                    logger.info(f"   🔗 Сканирую embeds в #{channel.name} (приоритет: {score:.1f})")
                    channel_members = 0

                    await self.rate_limiter.wait_if_needed('message_scan')

                    async for message in channel.history(limit=100):
                        stats.add_processed()

                        # Сообщения с embeds
                        if message.embeds:
                            # Автор сообщения с embed
                            if (message.author.id not in self.found_members and
                                    not message.author.bot):  # Проверка на бота
                                now = datetime.now().isoformat()
                                members_data.append((
                                    message.author.id,
                                    str(message.author),
                                    getattr(message.author, 'display_name', str(message.author)),
                                    "embed_author",
                                    channel.name,
                                    now,
                                    now
                                ))
                                self.found_members.add(message.author.id)
                                stats.add_member()
                                channel_members += 1

                            # Анализируем embed на предмет упоминаний пользователей
                            for embed in message.embeds:
                                if embed.description:
                                    # Ищем упоминания в описании embed
                                    mentions = re.findall(r'<@!?(\d+)>', embed.description)
                                    for user_id in mentions:
                                        try:
                                            user_id = int(user_id)
                                            if user_id not in self.found_members:
                                                # Пытаемся получить пользователя
                                                try:
                                                    user = await self.client.fetch_user(user_id)
                                                    if not user.bot:  # Проверка на бота
                                                        now = datetime.now().isoformat()
                                                        members_data.append((
                                                            user.id,
                                                            str(user),
                                                            getattr(user, 'display_name', str(user)),
                                                            "embed_mention",
                                                            channel.name,
                                                            now,
                                                            now
                                                        ))
                                                        self.found_members.add(user.id)
                                                        stats.add_member()
                                                        channel_members += 1
                                                except:
                                                    pass  # Пользователь не найден
                                        except ValueError:
                                            pass  # Неверный ID

                    logger.info(f"      ✅ {channel_members} участников")

                except Exception as e:
                    stats.add_error()
                    logger.debug(f"Ошибка сканирования embeds в {channel.name}: {e}")

            # Сохраняем найденных участников
            saved = self.save_members_batch(members_data)
            
        except Exception as e:
            logger.error(f"❌ Критическая ошибка в scan_embeds: {e}")
            stats.add_error()
        finally:
            stats.finish()

        logger.info(f"✅ Embeds: {stats.members_found} участников за {stats.time_taken:.2f}с "
                    f"({stats.efficiency:.1f} уч/с)")

        self.source_stats['embeds_scan'] = stats
        self.save_source_stats(stats)
        return stats

    def prioritize_channels(self, channels) -> List[Tuple]:
        """Ранжирование каналов по важности с улучшенной логикой"""
        ranked = []
        current_time = time.time()

        for channel in channels:
            score = 0

            # Приоритет по названию
            name_lower = channel.name.lower()
            if 'general' in name_lower: score += 30
            if 'welcome' in name_lower: score += 25
            if 'chat' in name_lower: score += 20
            if 'main' in name_lower: score += 15
            if 'discussion' in name_lower: score += 10
            if 'lobby' in name_lower: score += 18
            if 'announcements' in name_lower: score += 22
            if 'rules' in name_lower: score += 12
            if 'info' in name_lower: score += 15

            # Приоритет по активности (улучшенный)
            if channel.last_message_id:
                try:
                    message_time = channel.last_message_id.created_at.timestamp()
                    recency = current_time - message_time
                    
                    # Экспоненциальное убывание приоритета со временем
                    if recency < 3600:  # Последний час
                        score += 50
                    elif recency < 86400:  # Последние сутки
                        score += 30
                    elif recency < 604800:  # Последняя неделя
                        score += 15
                    else:
                        score += 5
                except:
                    pass

            # Приоритет по типу канала
            if hasattr(channel, 'type'):
                if channel.type == discord.ChannelType.text: score += 10
                if hasattr(discord.ChannelType, 'forum') and channel.type == discord.ChannelType.forum:
                    score += 20
                if hasattr(discord.ChannelType, 'news') and channel.type == discord.ChannelType.news:
                    score += 25

            # Бонус за позицию в списке (первые каналы обычно важнее)
            position_bonus = max(0, 10 - (channel.position / 10))
            score += position_bonus

            # Бонус за количество участников (если доступно)
            try:
                if hasattr(channel, 'member_count') and channel.member_count:
                    score += min(20, channel.member_count / 10)
            except:
                pass

            ranked.append((channel, score))

        return sorted(ranked, key=lambda x: x[1], reverse=True)

    async def start_realtime_monitoring(self):
        """Запуск фонового мониторинга в реальном времени"""
        try:
            logger.info("\n🔄 ПЕРЕХОД В РЕЖИМ ФОНОВОГО МОНИТОРИНГА")
            logger.info("📡 Отслеживаю новые сообщения в реальном времени...")
            logger.info("⏹️ Нажмите Ctrl+C для остановки")

            self.monitoring_active = True
            realtime_discoveries = 0
            last_stats_time = time.time()
        except Exception as e:
            logger.error(f"❌ Ошибка инициализации мониторинга: {e}")
            self.monitoring_active = False
            return

        # Настраиваем обработчики событий для мониторинга
        @self.client.event
        async def on_message(message):
            nonlocal realtime_discoveries

            try:
                if (message.guild and message.guild.id == self.guild_id and
                        not message.author.bot and
                        message.author.id not in self.found_members):
                    # Новый участник обнаружен!
                    now = datetime.now().isoformat()
                    member_data = [(
                        message.author.id,
                        str(message.author),
                        getattr(message.author, 'display_name', str(message.author)),
                        "realtime_message",
                        message.channel.name,
                        now,
                        now
                    )]

                    self.save_members_batch(member_data)
                    self.found_members.add(message.author.id)
                    realtime_discoveries += 1

                    # Сохраняем детали обнаружения
                    self.save_realtime_discovery(
                        message.author.id,
                        str(message.author),
                        message.channel.name,
                        message.content,
                        "new_message"
                    )

                    logger.info(f"🆕 НОВЫЙ УЧАСТНИК: {message.author} в #{message.channel.name}")
            except Exception as e:
                logger.error(f"❌ Ошибка обработки сообщения: {e}")

        @self.client.event
        async def on_reaction_add(reaction, user):
            nonlocal realtime_discoveries

            try:
                if (reaction.message.guild and reaction.message.guild.id == self.guild_id and
                        not user.bot and
                        user.id not in self.found_members):
                    # Новый участник через реакцию!
                    now = datetime.now().isoformat()
                    member_data = [(
                        user.id,
                        str(user),
                        getattr(user, 'display_name', str(user)),
                        "realtime_reaction",
                        reaction.message.channel.name,
                        now,
                        now
                    )]

                    self.save_members_batch(member_data)
                    self.found_members.add(user.id)
                    realtime_discoveries += 1

                    # Сохраняем детали обнаружения
                    self.save_realtime_discovery(
                        user.id,
                        str(user),
                        reaction.message.channel.name,
                        f"Реакция: {reaction.emoji}",
                        "new_reaction"
                    )

                    logger.info(f"🆕 НОВЫЙ УЧАСТНИК (реакция): {user} в #{reaction.message.channel.name}")
            except Exception as e:
                logger.error(f"❌ Ошибка обработки реакции: {e}")

        @self.client.event
        async def on_member_join(member):
            nonlocal realtime_discoveries

            try:
                if member.guild.id == self.guild_id and not member.bot:
                    # Новый участник присоединился!
                    now = datetime.now().isoformat()
                    member_data = [(
                        member.id,
                        str(member),
                        getattr(member, 'display_name', str(member)),
                        "realtime_join",
                        "server_join",
                        now,
                        now
                    )]

                    self.save_members_batch(member_data)
                    self.found_members.add(member.id)
                    realtime_discoveries += 1

                    # Сохраняем детали обнаружения
                    self.save_realtime_discovery(
                        member.id,
                        str(member),
                        "server_join",
                        "Присоединился к серверу",
                        "member_join"
                    )

                    logger.info(f"🆕 ПРИСОЕДИНИЛСЯ: {member}")
            except Exception as e:
                logger.error(f"❌ Ошибка обработки присоединения участника: {e}")

        # Запускаем непрерывный мониторинг memberlist в отдельном потоке
        logger.info("🔥 ЗАПУСК НЕПРЕРЫВНОГО МОНИТОРИНГА MEMBERLIST")
        memberlist_task = asyncio.create_task(self.continuous_memberlist_monitor())

        # Основной цикл мониторинга событий
        last_stats_time = time.time()

        try:
            while self.monitoring_active and not self.shutdown_event.is_set():
                try:
                    await asyncio.sleep(60)  # Проверяем каждую минуту

                    current_time = time.time()

                    # Показываем статистику каждые 5 минут
                    if current_time - last_stats_time > 300:  # 5 минут
                        try:
                            total_members = self.get_member_count()
                            coverage = (total_members / self.guild.member_count * 100) if self.guild.member_count > 0 else 0

                            logger.info(f"\n📊 СТАТИСТИКА НЕПРЕРЫВНОГО МОНИТОРИНГА:")
                            logger.info(f"   👥 Всего участников: {total_members:,}")
                            logger.info(f"   📈 Покрытие сервера: {coverage:.2f}%")
                            logger.info(f"   🆕 Найдено в реальном времени: {realtime_discoveries}")
                            logger.info(f"   🔥 Непрерывный мониторинг memberlist: АКТИВЕН")

                            last_stats_time = current_time
                        except Exception as e:
                            logger.error(f"❌ Ошибка вывода статистики мониторинга: {e}")

                except Exception as e:
                    logger.error(f"❌ Ошибка в цикле мониторинга: {e}")
                    await asyncio.sleep(10)  # Пауза перед повторной попыткой

        except KeyboardInterrupt:
            logger.info("\n🛑 Остановка мониторинга...")
        except Exception as e:
            logger.error(f"❌ Критическая ошибка мониторинга: {e}")
        finally:
            self.monitoring_active = False

            # Останавливаем непрерывный мониторинг
            try:
                if 'memberlist_task' in locals():
                    memberlist_task.cancel()
                    logger.info("🔥 Непрерывный мониторинг memberlist остановлен")
            except Exception as e:
                logger.error(f"❌ Ошибка остановки memberlist мониторинга: {e}")

            # Финальная статистика
            try:
                total_members = self.get_member_count()
                coverage = (total_members / self.guild.member_count * 100) if self.guild.member_count > 0 else 0

                logger.info(f"\n📊 ФИНАЛЬНАЯ СТАТИСТИКА МОНИТОРИНГА:")
                logger.info(f"   👥 Всего участников: {total_members:,}")
                logger.info(f"   📈 Покрытие сервера: {coverage:.2f}%")
                logger.info(f"   🆕 Найдено в реальном времени: {realtime_discoveries}")
                logger.info(f"   🔥 Непрерывный мониторинг memberlist: ЗАВЕРШЕН")
                logger.info("👋 Мониторинг завершен")
            except Exception as e:
                logger.error(f"❌ Ошибка вывода финальной статистики: {e}")

    async def ultra_fast_voice_scan(self) -> SourceStats:
        """Быстрое сканирование голосовых каналов"""
        stats = SourceStats("voice_scan")
        stats.start()

        logger.info("🎤 СКАНИРОВАНИЕ ГОЛОСОВЫХ КАНАЛОВ")

        members_data = []
        for voice_channel in self.guild.voice_channels:
            stats.add_processed()

            for member in voice_channel.members:
                if member.id not in self.found_members and not member.bot:
                    now = datetime.now().isoformat()
                    members_data.append((
                        member.id,
                        str(member),
                        getattr(member, 'display_name', str(member)),
                        "voice_scan",
                        voice_channel.name,
                        now,
                        now
                    ))
                    self.found_members.add(member.id)
                    stats.add_member()

        saved = self.save_members_batch(members_data)
        stats.finish()

        logger.info(f"✅ Голос: {stats.members_found} участников за {stats.time_taken:.2f}с "
                    f"({stats.efficiency:.1f} уч/с)")

        self.source_stats['voice_scan'] = stats
        self.save_source_stats(stats)
        return stats

    async def smart_message_scan(self, max_channels: int = 10, max_messages: int = 200) -> SourceStats:
        """Умное сканирование сообщений - авторы и упоминания с параллельным сканированием каналов"""
        stats = SourceStats("message_scan")
        stats.start()

        logger.info(f"📜 УМНОЕ СКАНИРОВАНИЕ СООБЩЕНИЙ ({max_channels} каналов) - ПАРАЛЛЕЛЬНОЕ")

        # Берем самые активные каналы
        text_channels = [ch for ch in self.guild.text_channels
                         if ch.permissions_for(self.guild.me).read_message_history]

        # Сортируем по ID последнего сообщения (активность)
        active_channels = sorted(text_channels, key=lambda x: x.last_message_id or 0, reverse=True)[:max_channels]

        # Параллельное сканирование каналов
        tasks = []
        for channel in active_channels:
            tasks.append(self.scan_channel_messages(channel, max_messages, stats))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        all_members = []
        for channel_members in results:
            if isinstance(channel_members, list):
                all_members.extend(channel_members)

        saved = self.save_members_batch(all_members)
        stats.finish()

        logger.info(f"✅ Сообщения: {stats.members_found} участников за {stats.time_taken:.2f}с "
                    f"({stats.efficiency:.1f} уч/с)")

        self.source_stats['message_scan'] = stats
        self.save_source_stats(stats)
        return stats

    async def scan_channel_messages(self, channel, max_messages: int, stats: SourceStats) -> List[tuple]:
        """Сканирование сообщений в отдельном канале"""
        channel_members = []
        
        try:
            logger.info(f"   📂 Сканирую #{channel.name}")
            await self.rate_limiter.wait_if_needed('message_scan')

            async for message in channel.history(limit=max_messages):
                stats.add_processed()

                # Авторы сообщений
                if message.author.id not in self.found_members and not message.author.bot:
                    now = datetime.now().isoformat()
                    channel_members.append((
                        message.author.id,
                        str(message.author),
                        getattr(message.author, 'display_name', str(message.author)),
                        "message_author",
                        channel.name,
                        now,
                        now
                    ))
                    self.found_members.add(message.author.id)
                    stats.add_member()

                # Упоминания
                for mentioned in message.mentions:
                    if mentioned.id not in self.found_members and not mentioned.bot:
                        now = datetime.now().isoformat()
                        channel_members.append((
                            mentioned.id,
                            str(mentioned),
                            getattr(mentioned, 'display_name', str(mentioned)),
                            "message_mention",
                            channel.name,
                            now,
                            now
                        ))
                        self.found_members.add(mentioned.id)
                        stats.add_member()

            logger.info(f"      ✅ {len(channel_members)} участников")

        except Exception as e:
            stats.add_error()
            logger.error(f"      ❌ Ошибка в {channel.name}: {e}")

        return channel_members

    async def run_enhanced_ultra_fast_scan(self, token: str, mode: str = "normal") -> Dict[str, SourceStats]:
        """Запуск расширенного ультра-быстрого сканирования с динамическим управлением ресурсами"""
        
        # Проверка токена перед запуском
        if not token:
            raise ValueError("❌ Токен не может быть пустым!")
        
        if len(token) < 50:
            raise ValueError(f"❌ Токен слишком короткий! Длина: {len(token)}, нужно минимум 50 символов")
        
        if token.startswith('Bot '):
            raise ValueError("❌ Используйте USER TOKEN, а не bot token!")
        
        # Проверяем формат токена (должен быть base64)
        try:
            import base64
            # Убираем возможные пробелы и проверяем base64
            clean_token = token.strip()
            if len(clean_token) % 4 != 0:
                raise ValueError("❌ Неверный формат токена (не base64)")
        except Exception as e:
            logger.warning(f"⚠️ Предупреждение о формате токена: {e}")
        
        logger.info(f"✅ Токен проверен (длина: {len(token)})")
        
        self.start_time = time.time()
        results = {}

        logger.info("🚀 РАСШИРЕННЫЙ УЛЬТРА-БЫСТРЫЙ DISCORD СКАНЕР v10.0")
        logger.info("⚡ Все возможные источники данных + фоновый мониторинг + ПРОДВИНУТЫЕ ОПТИМИЗАЦИИ!")
        logger.info("=" * 70)

        # Инициализация клиента
        # Настройка intents для discord.py-self 2.5+
        intents = discord.Intents.all()
        self.client = discord.Client(intents=intents)
        client_ready = asyncio.Event()

        @self.client.event
        async def on_ready():
            print(f"✅ Discord клиент готов! Подключен как: {self.client.user}")
            self.guild = self.client.get_guild(self.guild_id)
            if not self.guild:
                raise ValueError(f"Гильдия {self.guild_id} не найдена")

            logger.info(f"✅ Подключен к: {self.guild.name}")
            logger.info(f"👥 Участников на сервере: {self.guild.member_count:,}")

            # Инициализируем Bloom filter после получения объекта гильдии
            if BLOOM_FILTER_AVAILABLE and hasattr(self, 'guild') and self.guild:
                self.bloom_filter = OptimizedBloomFilter(
                    initial_capacity=self.guild.member_count * 2,
                    error_rate=0.001,
                    growth_factor=2
                )
                logger.info("✅ Оптимизированный Bloom filter инициализирован")
            else:
                self.bloom_filter = None
                logger.info("⚠️ Bloom filter недоступен, используется стандартное отслеживание")

            # Настраиваем адаптивные лимиты на основе размера сервера
            self.rate_limiter.update_limits_based_on_guild(self.guild)

            client_ready.set()

        # Запуск клиента
        try:
            print("🔄 Подключение к Discord для сканирования...")
            client_task = asyncio.create_task(self.client.start(token))
            
            # Добавляем таймаут
            try:
                await asyncio.wait_for(client_ready.wait(), timeout=30.0)
                print("✅ Подключение для сканирования успешно!")
            except asyncio.TimeoutError:
                print("❌ Таймаут подключения для сканирования (30 секунд)")
                if not client_task.done():
                    client_task.cancel()

                # Проверяем, есть ли исключение в задаче
                try:
                    await client_task
                except discord.LoginFailure:
                    print("❌ Неверный токен! Проверьте правильность user token.")
                    print("💡 Убедитесь, что вы используете USER token, а не bot token.")
                    print("💡 Токен должен быть получен из браузера (F12 -> Network -> Authorization)")
                except discord.HTTPException as e:
                    print(f"❌ Ошибка HTTP: {e.status} - {e}")
                    if e.status == 401:
                        print("💡 Токен недействителен или истек. Получите новый токен.")
                except Exception as e:
                    print(f"❌ Ошибка подключения: {e}")
                return results
                
        except discord.LoginFailure:
            logger.error("❌ Неверный токен! Проверьте правильность user token.")
            logger.error("💡 Убедитесь, что вы используете USER token, а не bot token.")
            return results
        except discord.HTTPException as e:
            logger.error(f"❌ Ошибка HTTP: {e.status} - {e}")
            return results
        except Exception as e:
            logger.error(f"❌ Ошибка подключения: {e}")
            return results

        try:
            # Принудительный сброс для точной статистики
            logger.info("🔄 Сброс состояния для точной статистики...")
            self.found_members = set()
            if self.bloom_filter:
                self.bloom_filter = OptimizedBloomFilter(
                    initial_capacity=self.guild.member_count * 2 if self.guild else 100000,
                    error_rate=0.005,
                    growth_factor=1.5
                )
            
            # Динамическое управление ресурсами на основе размера сервера
            adaptive_params = self.rate_limiter.adaptive_scanning_params(self.guild, mode)
            
            # Автоматическое определение стратегии для больших серверов
            if self.guild.member_count > 20000:
                logger.info("🏢 ОБНАРУЖЕН БОЛЬШОЙ СЕРВЕР (>20k участников)")
                logger.info("🛠️ АКТИВИРОВАН СПЕЦИАЛЬНЫЙ РЕЖИМ СКАНИРОВАНИЯ")
                
                # Увеличиваем интервалы и уменьшаем агрессивность
                adaptive_params = {
                    'max_channels': 5,
                    'max_messages': 50,
                    'max_reactions': 20,
                    'scan_interval': 600,
                    'batch_size': 50,
                    'retry_attempts': 5
                }
                
                # Приоритет безопасных методов
                priority_methods = [
                    self.enhanced_cache_scan,
                    self.scan_system_messages,
                    self.ultra_fast_voice_scan
                ]
            
            if mode == "turbo":
                logger.info("🚀 РЕЖИМ TURBO АКТИВИРОВАН - МАКСИМАЛЬНАЯ СКОРОСТЬ!")
                logger.info("⚡ Параллельное выполнение 20+ задач + агрессивные настройки")
                
                # TURBO MODE: Параллельный запуск ТОП-6 методов
                logger.info("🔥 ЗАПУСК ТОП-6 МЕТОДОВ ПАРАЛЛЕЛЬНО...")
                priority_tasks = await asyncio.gather(
                    self.enhanced_memberlist_scan(),
                    self.scan_full_memberlist(),  # Добавлен полный список участников
                    self.smart_message_scan(adaptive_params['max_channels'] * 3, adaptive_params['max_messages'] * 3),
                    self.optimized_reaction_scan(adaptive_params['max_channels'] * 3, adaptive_params['max_messages'] * 3),
                    self.scan_system_messages(),  # Системные сообщения
                    self.scan_events(),  # События сервера
                    return_exceptions=True
                )
                
                # TURBO MODE: Параллельный запуск вторичных методов
                logger.info("🔥 ЗАПУСК ВТОРИЧНЫХ МЕТОДОВ ПАРАЛЛЕЛЬНО...")
                secondary_tasks = await asyncio.gather(
                    self.ultra_fast_voice_scan(),
                    self.scan_notifications(),  # Новый метод уведомлений
                    self.deep_problem_member_scan(),  # Глубокое сканирование
                    return_exceptions=True
                )
                
                # TURBO MODE: Фоновый запуск дополнительных методов
                logger.info("🔥 ЗАПУСК ФОНОВЫХ МЕТОДОВ...")
                background_tasks = [
                    asyncio.create_task(self.scan_threads_and_forums()),
                    asyncio.create_task(self.scan_embeds()),
                    asyncio.create_task(self.scan_invites_and_roles()),
                    asyncio.create_task(self.scan_pinned_messages()),
                    asyncio.create_task(self.scan_integrations()),
                    asyncio.create_task(self.scan_audit_log()),
                    asyncio.create_task(self.scan_activities()),
                    asyncio.create_task(self.scan_application_commands()),
                    asyncio.create_task(self.analyze_activity_patterns()),
                    asyncio.create_task(self.scan_notifications()),  # Дополнительный запуск уведомлений
                    asyncio.create_task(self.deep_problem_member_scan())  # Дополнительный запуск глубокого сканирования
                ]
                await asyncio.sleep(0)  # Yield control для фоновых задач
                
                logger.info("✅ TURBO MODE: Все методы запущены параллельно!")
                
                # Обрабатываем результаты TURBO методов
                priority_names = ['enhanced_memberlist', 'full_memberlist', 'smart_messages', 'reactions', 'system_messages', 'events']
                for i, result in enumerate(priority_tasks):
                    if isinstance(result, Exception):
                        logger.error(f"❌ Ошибка в {priority_names[i]}: {result}")
                        results[priority_names[i]] = SourceStats(priority_names[i])
                    else:
                        results[priority_names[i]] = result
                
                logger.info("✅ Обработка приоритетных методов завершена")
                
                secondary_names = ['voice_scan', 'notifications', 'deep_problem_scan']
                for i, result in enumerate(secondary_tasks):
                    if isinstance(result, Exception):
                        logger.error(f"❌ Ошибка в {secondary_names[i]}: {result}")
                        results[secondary_names[i]] = SourceStats(secondary_names[i])
                    else:
                        results[secondary_names[i]] = result
                
                logger.info("✅ Обработка вторичных методов завершена")
                
                # Ждем завершения фоновых задач
                logger.info("⏳ Ожидание завершения фоновых задач...")
                try:
                    background_results = await asyncio.gather(*background_tasks, return_exceptions=True)
                    logger.info("✅ Фоновые задачи завершены")
                except Exception as e:
                    logger.error(f"❌ Ошибка при выполнении фоновых задач: {e}")
                    background_results = [SourceStats(name) for name in background_names]
                
                background_names = ['threads_forums', 'embeds', 'invites_roles', 'pinned_messages', 'integrations', 'audit_log', 'activities', 'application_commands', 'activity_patterns', 'notifications', 'deep_problem_scan']
                for i, result in enumerate(background_results):
                    if isinstance(result, Exception):
                        logger.error(f"❌ Ошибка в {background_names[i]}: {result}")
                        results[background_names[i]] = SourceStats(background_names[i])
                    else:
                        results[background_names[i]] = result
                
                logger.info("✅ Обработка фоновых задач завершена")
                
                # Переходим к финальной статистике
                goto_final_stats = True
                
            elif self.guild.member_count > 10000:
                priority_methods = [
                    self.scan_full_memberlist,  # ПЕРВЫМ идет full_memberlist
                    self.enhanced_memberlist_scan,  # ВТОРЫМ идет enhanced_memberlist
                    self.enhanced_cache_scan,
                    self.ultra_fast_voice_scan
                ]
                logger.info("🔧 Адаптивная стратегия для большого сервера (>10k участников)")
                logger.info(f"   📊 Параметры: {adaptive_params['max_channels']} каналов, {adaptive_params['max_messages']} сообщений")
                goto_final_stats = False
            else:
                priority_methods = [
                    self.scan_full_memberlist,  # ПЕРВЫМ идет full_memberlist
                    self.enhanced_memberlist_scan,  # ВТОРЫМ идет enhanced_memberlist
                    lambda: self.smart_message_scan(adaptive_params['max_channels'], adaptive_params['max_messages']),
                    lambda: self.optimized_reaction_scan(adaptive_params['max_channels'], adaptive_params['max_messages']),
                    self.scan_system_messages
                ]
                logger.info("🔧 Адаптивная стратегия для среднего/малого сервера")
                logger.info(f"   📊 Параметры: {adaptive_params['max_channels']} каналов, {adaptive_params['max_messages']} сообщений")
                goto_final_stats = False

            # БЫСТРЫЕ МЕТОДЫ С ВЫСОКИМ ROI (только для обычного режима)
            if not goto_final_stats:
                logger.info("\n" + "=" * 70)
                logger.info("⚡ БЫСТРЫЕ МЕТОДЫ С ВЫСОКИМ ROI")
                logger.info("=" * 70)

                # Запускаем приоритетные методы первыми
                tasks = [method() for method in priority_methods]
                results_priority = await asyncio.gather(*tasks, return_exceptions=True)

                            # Обрабатываем результаты приоритетных методов (только для обычного режима)
                if not goto_final_stats:
                    priority_names = ['full_memberlist', 'enhanced_memberlist', 'cache', 'voice', 'messages', 'reactions', 'system']
                    for i, result in enumerate(results_priority):
                        if isinstance(result, Exception):
                            logger.error(f"❌ Ошибка в {priority_names[i]}: {result}")
                            results[priority_names[i]] = SourceStats(priority_names[i])
                        else:
                            results[priority_names[i]] = result
                    
                    logger.info("✅ Обработка приоритетных методов (обычный режим) завершена")

                # ДОПОЛНИТЕЛЬНЫЕ МЕТОДЫ
                logger.info("\n" + "=" * 70)
                logger.info("📊 ДОПОЛНИТЕЛЬНЫЕ МЕТОДЫ СКАНИРОВАНИЯ")
                logger.info("=" * 70)

                # Параллельное выполнение дополнительных задач
                additional_tasks = [
                    self.scan_threads_and_forums(),
                    self.scan_events(),
                    self.scan_full_memberlist(),
                    self.scan_embeds(),
                    self.scan_invites_and_roles(),
                    self.scan_pinned_messages(),
                    self.scan_integrations(),
                    self.scan_audit_log(),
                    self.scan_activities(),
                    self.scan_application_commands(),
                    self.analyze_activity_patterns(),
                    self.scan_notifications(),  # Новый метод уведомлений
                    self.deep_problem_member_scan()  # Глубокое сканирование
                ]

                logger.info("🔄 ПАРАЛЛЕЛЬНОЕ ВЫПОЛНЕНИЕ ДОПОЛНИТЕЛЬНЫХ МЕТОДОВ...")
                additional_results = await asyncio.gather(*additional_tasks, return_exceptions=True)

                # Обрабатываем результаты дополнительных методов
                additional_names = ['threads_forums', 'events', 'full_memberlist', 'embeds', 'invites_roles', 'pinned_messages', 'integrations', 'audit_log', 'activities', 'application_commands', 'activity_patterns', 'notifications', 'deep_problem_scan']
                for i, result in enumerate(additional_results):
                    if isinstance(result, Exception):
                        logger.error(f"❌ Ошибка в {additional_names[i]}: {result}")
                        results[additional_names[i]] = SourceStats(additional_names[i])
                    else:
                        results[additional_names[i]] = result
                
                logger.info("✅ Обработка дополнительных методов завершена")

            # ФИНАЛЬНАЯ СТАТИСТИКА ПЕРВИЧНОГО СКАНИРОВАНИЯ
            total_time = time.time() - self.start_time
            total_found = len(self.found_members)
            coverage = (total_found / self.guild.member_count * 100) if self.guild.member_count > 0 else 0

            logger.info("\n" + "=" * 70)
            logger.info("🏁 РЕЗУЛЬТАТЫ ПЕРВИЧНОГО СКАНИРОВАНИЯ")
            logger.info("=" * 70)
            try:
                logger.info(f"⏱️  Общее время сканирования: {total_time:.1f} секунд")
                logger.info(f"👥 Найдено участников: {total_found:,}")
                logger.info(f"📊 Покрытие сервера: {coverage:.2f}%")
                logger.info(f"⚡ Общая скорость: {total_found / total_time:.1f} участников/сек")
                logger.info("")
                logger.info("📈 ДЕТАЛЬНАЯ СТАТИСТИКА ПО ИСТОЧНИКАМ:")
                logger.info("-" * 70)
            except Exception as e:
                logger.error(f"❌ Ошибка вывода итоговой статистики: {e}")

            # Обновляем адаптивную стратегию на основе результатов
            try:
                for source_name, stats in self.source_stats.items():
                    try:
                        success = getattr(stats, 'errors', 0) == 0 and getattr(stats, 'members_found', 0) > 0
                        efficiency = getattr(stats, 'efficiency', 0) or 0
                        self.scan_strategy.update(source_name, efficiency, success)
                        
                        # Увеличиваем вес источников, которые нашли участников
                        if getattr(stats, 'members_found', 0) > 0:
                            self.scan_strategy.source_weights[source_name] *= 1.5
                        # Уменьшаем вес источников с ошибками
                        if getattr(stats, 'errors', 0) > 0:
                            self.scan_strategy.source_weights[source_name] *= 0.7
                    except Exception as e:
                        logger.error(f"❌ Ошибка обновления стратегии для {source_name}: {e}")
            except Exception as e:
                logger.error(f"❌ Ошибка обновления адаптивной стратегии: {e}")
            
            # Получаем топ источников (увеличиваем до 8)
            try:
                top_sources = self.scan_strategy.get_top_sources(8)
                logger.info(f"🔧 Топ-8 эффективных источников: {', '.join(top_sources)}")
            except Exception as e:
                logger.error(f"❌ Ошибка получения топ источников: {e}")
                top_sources = []
            
            # Автоматическое переключение стратегии при дисбалансе
            try:
                total_found = sum(getattr(stats, 'members_found', 0) for stats in self.source_stats.values())
                if total_found > 0:
                    reaction_stats = self.source_stats.get('reaction_scan')
                    if reaction_stats:
                        reaction_percentage = (getattr(reaction_stats, 'members_found', 0) / total_found) * 100
                        if reaction_percentage > 70:
                            logger.warning("⚖️ Дисбаланс источников! Увеличиваю приоритет других методов")
                            # Уменьшаем вес reaction scan
                            self.scan_strategy.source_weights['reaction_scan'] *= 0.6
                            # Увеличиваем приоритет альтернативных методов
                            for source in ['threads_forums', 'system_messages', 'events', 'notifications', 'deep_problem_scan']:
                                self.scan_strategy.source_weights[source] *= 1.8
            except Exception as e:
                logger.error(f"❌ Ошибка анализа дисбаланса источников: {e}")
                total_found = 0
            
            # Сортируем по эффективности с обработкой ошибок
            try:
                sorted_stats = sorted(self.source_stats.items(),
                                      key=lambda x: getattr(x[1], 'efficiency', 0) or 0, reverse=True)

                for source_name, stats in sorted_stats:
                    try:
                        logger.info(str(stats))
                    except Exception as e:
                        logger.error(f"❌ Ошибка вывода статистики для {source_name}: {e}")
            except Exception as e:
                logger.error(f"❌ Ошибка сортировки статистики: {e}")
                # Выводим статистику без сортировки
                for source_name, stats in self.source_stats.items():
                    try:
                        logger.info(str(stats))
                    except Exception as e:
                        logger.error(f"❌ Ошибка вывода статистики для {source_name}: {e}")

            logger.info("=" * 70)

            # Переход к фоновому мониторингу
            try:
                if total_found > 0:
                    logger.info(f"🎯 Первичное сканирование завершено!")
                    logger.info(f"📡 Переходим к фоновому мониторингу в реальном времени...")

                    # Запускаем фоновый мониторинг
                    await self.start_realtime_monitoring()
                    await asyncio.Event().wait()  # Не даём завершиться процессу
                else:
                    logger.info("⚠️ Участники не найдены. Фоновый мониторинг не запускается.")
            except Exception as e:
                logger.error(f"❌ Ошибка перехода к фоновому мониторингу: {e}")
                # Даже при ошибке пытаемся запустить мониторинг
                try:
                    await self.start_realtime_monitoring()
                    await asyncio.Event().wait()
                except Exception as e2:
                    logger.error(f"❌ Критическая ошибка запуска мониторинга: {e2}")

            return results

        finally:
            try:
                await self.client.close()
            except Exception as e:
                logger.error(f"❌ Ошибка закрытия клиента: {e}")
            
            try:
                if not client_task.done():
                    client_task.cancel()
            except Exception as e:
                logger.error(f"❌ Ошибка отмены задачи клиента: {e}")

    async def scan_notifications(self) -> SourceStats:
        """Сканирование уведомлений и упоминаний"""
        stats = SourceStats("notifications")
        stats.start()
        
        logger.info("🔔 СКАНИРОВАНИЕ УВЕДОМЛЕНИЙ И УПОМИНАНИЙ")
        
        members_data = []
        
        try:
            # Сканируем аудит лог для уведомлений о членах
            async for entry in self.guild.audit_logs(limit=100, action=discord.AuditLogAction.member_update):
                stats.add_processed()
                
                if entry.user and not entry.user.bot:
                    if not self.is_member_found(entry.user.id):
                        now = datetime.now().isoformat()
                        members_data.append((
                            entry.user.id,
                            str(entry.user),
                            getattr(entry.user, 'display_name', str(entry.user)),
                            "notification_audit",
                            f"audit_{entry.action}",
                            now,
                            now
                        ))
                        await self.mark_member_found(entry.user.id)
                        stats.add_member()
                        
                        # Сохраняем роли участника
                        if hasattr(entry.user, 'roles'):
                            self.save_member_roles(entry.user.id, entry.user.roles)
            
            # Сканируем системные каналы на упоминания
            system_channels = []
            for channel in self.guild.text_channels:
                if ('welcome' in channel.name.lower() or 
                    'general' in channel.name.lower() or 
                    'announcements' in channel.name.lower() or
                    'system' in channel.name.lower() or
                    channel == self.guild.system_channel):
                    system_channels.append(channel)
            
            system_channels = system_channels[:20]  # Ограничиваем до 20 каналов
            
            for channel in system_channels:
                try:
                    await self.rate_limiter.wait_if_needed('message_scan')
                    
                    async for message in channel.history(limit=50):
                        stats.add_processed()
                        
                        # Обрабатываем упоминания в сообщениях
                        for mention in message.mentions:
                            if not mention.bot and not self.is_member_found(mention.id):
                                now = datetime.now().isoformat()
                                members_data.append((
                                    mention.id,
                                    str(mention),
                                    getattr(mention, 'display_name', str(mention)),
                                    "notification_mention",
                                    channel.name,
                                    now,
                                    now
                                ))
                                await self.mark_member_found(mention.id)
                                stats.add_member()
                                
                                # Сохраняем роли участника
                                if hasattr(mention, 'roles'):
                                    self.save_member_roles(mention.id, mention.roles)
                        
                        # Обрабатываем автора сообщения
                        if (message.author and not message.author.bot and 
                            not self.is_member_found(message.author.id)):
                            now = datetime.now().isoformat()
                            members_data.append((
                                message.author.id,
                                str(message.author),
                                getattr(message.author, 'display_name', str(message.author)),
                                "notification_author",
                                channel.name,
                                now,
                                now
                            ))
                            await self.mark_member_found(message.author.id)
                            stats.add_member()
                            
                            # Сохраняем роли участника
                            if hasattr(message.author, 'roles'):
                                self.save_member_roles(message.author.id, message.author.roles)
                
                except Exception as e:
                    logger.debug(f"Ошибка сканирования уведомлений в {channel.name}: {e}")
                    stats.add_error()
        
        except Exception as e:
            logger.error(f"❌ Ошибка сканирования уведомлений: {e}")
            stats.add_error()
        
        # Сохранение результатов
        if members_data:
            await self.save_members_async(members_data)
        
        stats.finish()
        logger.info(f"✅ Уведомления: {stats.members_found} участников за {stats.time_taken:.2f}с ({stats.efficiency:.1f} уч/с)")
        
        self.source_stats['notifications'] = stats
        self.save_source_stats(stats)
        return stats

    async def enhanced_api_member_fetch(self) -> list:
        """Расширенное получение участников через API с повторными попытками"""
        logger.info("🔍 Расширенное получение участников через API...")
        
        for attempt in range(3):  # 3 попытки
            try:
                await self.rate_limiter.wait_if_needed('memberlist_scan')
                logger.info(f"   📊 Попытка {attempt+1}/3 получения участников через API...")
                
                try:
                    members = await self.guild.fetch_members()
                    logger.info(f"   ✅ API участников получено: {len(members)}")
                    return members
                except discord.HTTPException as e:
                    if e.status == 429:
                        wait = e.retry_after or 10
                        logger.warning(f"   ⏳ Rate limit, ожидание {wait}с...")
                        await asyncio.sleep(wait)
                    else:
                        raise
                except Exception as e:
                    if "Failed to automatically choose channels" in str(e):
                        logger.warning("   ⚠️ Требуется ручной выбор каналов")
                        available_channels = [ch for ch in self.guild.text_channels 
                                             if ch.permissions_for(self.guild.me).read_message_history]
                        
                        # Приоритет для важных каналов
                        def channel_priority(ch):
                            name = ch.name.lower()
                            if 'general' in name: return 3
                            if 'welcome' in name: return 2
                            if 'main' in name: return 1
                            if 'chat' in name: return 1
                            return 0
                        
                        active_channels = sorted(available_channels, 
                                                key=lambda x: (channel_priority(x), x.last_message_id or 0), 
                                                reverse=True)[:10]  # Увеличено до 10 каналов
                        
                        logger.info(f"   🔍 Выбраны каналы: {', '.join([ch.name for ch in active_channels])}")
                        
                        members = set()
                        for channel in active_channels:
                            try:
                                logger.info(f"      📂 Сканирую #{channel.name}...")
                                async for message in channel.history(limit=200):  # Увеличено до 200 сообщений
                                    if message.author and isinstance(message.author, discord.Member):
                                        members.add(message.author)
                            except Exception as e:
                                logger.error(f"      ❌ Ошибка в {channel.name}: {e}")
                        return list(members)
                    else:
                        raise
            
            except Exception as e:
                logger.error(f"   ❌ Ошибка API (попытка {attempt+1}/3): {e}")
                if attempt == 2:
                    logger.warning("   ⚠️ Не удалось получить участников через API после 3 попыток")
                    return []
        
        return []

    async def deep_problem_member_scan(self) -> SourceStats:
        """Углубленное сканирование проблемных участников"""
        stats = SourceStats("deep_problem_scan")
        stats.start()
        
        logger.info("🔍 УГЛУБЛЕННОЕ СКАНИРОВАНИЕ ПРОБЛЕМНЫХ УЧАСТНИКОВ")
        
        members_data = []
        
        # Получаем проблемных участников
        problem_ids = self.get_problem_member_ids()
        logger.info(f"   📊 Найдено проблемных участников: {len(problem_ids)}")
        
        if not problem_ids:
            stats.finish()
            return stats
        
        # Используем 5 независимых методов проверки
        verification_methods = [
            self.check_member_via_roles,
            self.check_member_via_connections,
            self.check_member_via_common_servers,
            self.check_member_via_activity,
            self.check_member_via_reactions
        ]
        
        # Применяем все методы к проблемным участникам
        for user_id in list(problem_ids)[:50]:  # Ограничиваем до 50 участников
            stats.add_processed()
            
            try:
                # Пытаемся получить участника через различные методы
                member_found = False
                
                for method in verification_methods:
                    try:
                        member = await method(user_id)
                        if member and not member.bot:
                            now = datetime.now().isoformat()
                            members_data.append((
                                member.id,
                                str(member),
                                getattr(member, 'display_name', str(member)),
                                "deep_problem_scan",
                                f"method_{method.__name__}",
                                now,
                                now
                            ))
                            await self.mark_member_found(member.id)
                            stats.add_member()
                            member_found = True
                            
                            # Сохраняем роли участника
                            if hasattr(member, 'roles'):
                                self.save_member_roles(member.id, member.roles)
                            break
                    except Exception as e:
                        logger.debug(f"Ошибка метода {method.__name__} для {user_id}: {e}")
                        continue
                
                if member_found:
                    logger.info(f"   ✅ Проблемный участник найден: {user_id}")
                
            except Exception as e:
                logger.debug(f"Ошибка обработки проблемного участника {user_id}: {e}")
                stats.add_error()
        
        # Сохранение результатов
        if members_data:
            await self.save_members_async(members_data)
        
        stats.finish()
        logger.info(f"✅ Глубокое сканирование: {stats.members_found} участников за {stats.time_taken:.2f}с")
        
        self.source_stats['deep_problem_scan'] = stats
        self.save_source_stats(stats)
        return stats

    async def check_member_via_roles(self, user_id: int):
        """Проверка участника через роли"""
        try:
            for role in self.guild.roles:
                for member in role.members:
                    if member.id == user_id:
                        return member
        except:
            pass
        return None

    async def check_member_via_connections(self, user_id: int):
        """Проверка участника через связи"""
        try:
            # Пытаемся получить участника напрямую
            return await self.guild.fetch_member(user_id)
        except:
            pass
        return None

    async def check_member_via_common_servers(self, user_id: int):
        """Проверка участника через общие серверы"""
        try:
            # Проверяем, есть ли участник в кэше
            for member in self.guild.members:
                if member.id == user_id:
                    return member
        except:
            pass
        return None

    async def check_member_via_activity(self, user_id: int):
        """Проверка участника через активность"""
        try:
            # Проверяем голосовые каналы
            for voice_channel in self.guild.voice_channels:
                for member in voice_channel.members:
                    if member.id == user_id:
                        return member
        except:
            pass
        return None

    async def check_member_via_reactions(self, user_id: int):
        """Проверка участника через реакции"""
        try:
            # Проверяем последние сообщения на реакции
            for channel in self.guild.text_channels[:5]:  # Ограничиваем каналы
                try:
                    async for message in channel.history(limit=20):
                        for reaction in message.reactions:
                            async for user in reaction.users():
                                if user.id == user_id:
                                    return user
                except:
                    continue
        except:
            pass
        return None

    async def setup_event_driven_monitoring(self):
        """Настройка event-driven мониторинга для замены continuous_memberlist_monitor"""
        logger.info("🔧 НАСТРОЙКА EVENT-DRIVEN МОНИТОРИНГА")
        logger.info("   📡 Использует события Discord вместо постоянного сканирования")

        # Создаем обработчики событий для мониторинга
        @self.client.event
        async def on_member_join(member):
            """Обработка присоединения нового участника"""
            if not member.bot and not self.is_member_found(member.id):
                try:
                    now = datetime.now().isoformat()
                    member_data = [(
                        member.id,
                        str(member),
                        getattr(member, 'display_name', str(member)),
                        "event_member_join",
                        f"joined_{member.guild.name}",
                        now,
                        now
                    )]

                    # Сохраняем нового участника
                    self.save_members_batch(member_data)
                    await self.mark_member_found(member.id)

                    # Сохраняем роли
                    if hasattr(member, 'roles'):
                        self.save_member_roles(member.id, member.roles)

                    # Сохраняем в реальном времени
                    self.save_realtime_discovery(
                        member.id, str(member), "server_join",
                        f"Joined {member.guild.name}", "member_join_event"
                    )

                    logger.info(f"🆕 EVENT: Новый участник присоединился: {member}")

                except Exception as e:
                    logger.error(f"❌ Ошибка обработки присоединения {member}: {e}")

        @self.client.event
        async def on_member_update(before, after):
            """Обработка обновления участника"""
            if not after.bot and not self.is_member_found(after.id):
                try:
                    # Проверяем, изменились ли роли или статус
                    roles_changed = before.roles != after.roles
                    status_changed = getattr(before, 'status', None) != getattr(after, 'status', None)

                    if roles_changed or status_changed:
                        now = datetime.now().isoformat()
                        member_data = [(
                            after.id,
                            str(after),
                            getattr(after, 'display_name', str(after)),
                            "event_member_update",
                            f"updated_{after.guild.name}",
                            now,
                            now
                        )]

                        # Сохраняем обновленного участника
                        self.save_members_batch(member_data)
                        await self.mark_member_found(after.id)

                        # Обновляем роли
                        if hasattr(after, 'roles'):
                            self.save_member_roles(after.id, after.roles)

                        # Сохраняем в реальном времени
                        update_type = "role_update" if roles_changed else "status_update"
                        self.save_realtime_discovery(
                            after.id, str(after), "member_update",
                            f"Updated in {after.guild.name}", update_type
                        )

                        logger.info(f"🔄 EVENT: Участник обновлен: {after} (роли: {roles_changed}, статус: {status_changed})")

                except Exception as e:
                    logger.error(f"❌ Ошибка обработки обновления {after}: {e}")

        @self.client.event
        async def on_message(message):
            """Обработка новых сообщений для обнаружения участников"""
            if (message.author and not message.author.bot and
                not self.is_member_found(message.author.id)):
                try:
                    now = datetime.now().isoformat()
                    member_data = [(
                        message.author.id,
                        str(message.author),
                        getattr(message.author, 'display_name', str(message.author)),
                        "event_message_author",
                        f"message_{message.channel.name}",
                        now,
                        now
                    )]

                    # Сохраняем автора сообщения
                    self.save_members_batch(member_data)
                    await self.mark_member_found(message.author.id)

                    # Сохраняем роли
                    if hasattr(message.author, 'roles'):
                        self.save_member_roles(message.author.id, message.author.roles)

                    # Сохраняем в реальном времени (только для важных каналов)
                    if any(keyword in message.channel.name.lower()
                           for keyword in ['general', 'chat', 'main', 'welcome']):
                        self.save_realtime_discovery(
                            message.author.id, str(message.author),
                            message.channel.name, message.content[:100], "message_event"
                        )

                    logger.debug(f"💬 EVENT: Новое сообщение от: {message.author} в #{message.channel.name}")

                except Exception as e:
                    logger.debug(f"❌ Ошибка обработки сообщения от {message.author}: {e}")

        @self.client.event
        async def on_voice_state_update(member, before, after):
            """Обработка изменений голосового состояния"""
            if not member.bot and not self.is_member_found(member.id):
                try:
                    # Участник присоединился к голосовому каналу
                    if before.channel is None and after.channel is not None:
                        now = datetime.now().isoformat()
                        member_data = [(
                            member.id,
                            str(member),
                            getattr(member, 'display_name', str(member)),
                            "event_voice_join",
                            f"voice_{after.channel.name}",
                            now,
                            now
                        )]

                        # Сохраняем участника
                        self.save_members_batch(member_data)
                        await self.mark_member_found(member.id)

                        # Сохраняем роли
                        if hasattr(member, 'roles'):
                            self.save_member_roles(member.id, member.roles)

                        # Сохраняем в реальном времени
                        self.save_realtime_discovery(
                            member.id, str(member), after.channel.name,
                            f"Joined voice channel", "voice_join_event"
                        )

                        logger.info(f"🎤 EVENT: Участник присоединился к голосовому каналу: {member} → {after.channel.name}")

                except Exception as e:
                    logger.error(f"❌ Ошибка обработки голосового события {member}: {e}")

        logger.info("✅ Event-driven мониторинг настроен")
        logger.info("   📡 Активны обработчики: member_join, member_update, message, voice_state_update")


class EnhancedUltraFastMonitor:
    """Главный класс ультра-быстрого монитора"""

    def get_token(self) -> str:
        load_dotenv()
        token = os.getenv('DISCORD_TOKEN')
        
        print(f"🔍 Проверка токена из .env файла...")
        if token:
            if token == 'your_user_token_here':
                print("❌ Токен не настроен в .env файле")
            else:
                return self.validate_token(token)
        else:
            print("❌ Токен не найден в .env файле")

        print("\n🔑 ВАЖНО: Вам нужен USER TOKEN (не bot token)!")
        print("📋 Как получить user token:")
        print("   1. Откройте Discord в браузере")
        print("   2. Нажмите F12 (Developer Tools)")
        print("   3. Перейдите во вкладку Network")
        print("   4. В Discord найдите любой запрос к API")
        print("   5. В заголовках найдите 'authorization'")
        print("   6. Скопируйте значение (без 'Bot ' префикса)")
        print()
        
        token = input("Введите Discord USER токен: ").strip()
        if not token:
            raise ValueError("Токен не может быть пустым!")
        
        return self.validate_token(token)

    def validate_token(self, token: str) -> str:
        """Расширенная валидация токена"""
        if not token or len(token) < 50:
            raise ValueError("Недопустимый токен!")
        
        # Очищаем токен от лишних символов
        clean_token = token.strip().strip('"\'')
        print(f"✅ Токен найден (длина: {len(clean_token)})")
        
        # Проверяем тип токена
        if clean_token.startswith('Bot '):
            print("❌ ОШИБКА: Обнаружен префикс 'Bot ' - это bot token!")
            print("💡 Нужен USER token (без префикса 'Bot ')")
            raise ValueError("Используйте USER TOKEN, а не bot token!")
        
        # Проверяем длину
        if len(clean_token) < 50:
            print(f"❌ ОШИБКА: Токен слишком короткий ({len(clean_token)} символов)")
            print("💡 User token обычно имеет 72+ символов")
            raise ValueError("Токен слишком короткий!")
        
        if len(clean_token) > 100:
            print(f"⚠️ Токен очень длинный ({len(clean_token)} символов)")
        
        # Проверяем формат токена
        if '.' not in clean_token:
            raise ValueError("❌ Неверный формат токена! Убедитесь, что это USER token")
        
        # Проверяем префикс
        if clean_token.startswith('mfa.'):
            logger.warning("⚠️ Обнаружен MFA токен - убедитесь, что он действителен долгое время")
        
        # Проверяем формат base64
        try:
            import base64
            if len(clean_token) % 4 != 0:
                print("⚠️ Предупреждение: Токен не соответствует формату base64")
        except:
            pass
        
        # Базовая проверка структуры
        try:
            parts = clean_token.split('.')
            if len(parts) != 3:
                print("⚠️ Предупреждение: Необычная структура токена")
        except:
            print("⚠️ Предупреждение: Необычный формат токена")
        
        print(f"✅ Токен выглядит корректно")
        return clean_token

    async def select_guild(self, client) -> discord.Guild:
        guilds = client.guilds
        if not guilds:
            raise ValueError("Нет доступных серверов!")

        print(f"\n📊 Доступные серверы ({len(guilds)}):")
        for i, guild in enumerate(guilds, 1):
            print(f" {i}. {guild.name} ({guild.member_count:,} участников)")

        while True:
            try:
                choice = int(input(f"\nВыберите сервер (1-{len(guilds)}): ")) - 1
                if 0 <= choice < len(guilds):
                    return guilds[choice]
                print("❌ Неверный выбор!")
            except ValueError:
                print("❌ Введите число!")

    async def run(self):
        try:
            token = self.get_token()

            # Выбор сервера
            # Настройка intents для discord.py-self 2.5+
            intents = discord.Intents.all()
            temp_client = discord.Client(intents=intents)
            selected_guild_id = None
            client_ready = asyncio.Event()

            @temp_client.event
            async def on_ready():
                nonlocal selected_guild_id
                try:
                    selected_guild = await self.select_guild(temp_client)
                    selected_guild_id = selected_guild.id
                except Exception as e:
                    logger.error(f"❌ Ошибка выбора сервера: {e}")
                finally:
                    client_ready.set()
                    await temp_client.close()

            try:
                print("🔄 Подключение к Discord...")
                client_task = asyncio.create_task(temp_client.start(token))

                # Добавляем таймаут
                try:
                    await asyncio.wait_for(client_ready.wait(), timeout=30.0)
                    print("✅ Подключение успешно!")
                except asyncio.TimeoutError:
                    print("❌ Таймаут подключения (30 секунд)")
                    if not client_task.done():
                        client_task.cancel()

                    # Проверяем, есть ли исключение в задаче
                    try:
                        await client_task
                    except discord.LoginFailure:
                        print("❌ Неверный токен! Проверьте правильность user token.")
                        print("💡 Убедитесь, что вы используете USER token, а не bot token.")
                        print("💡 Токен должен быть получен из браузера (F12 -> Network -> Authorization)")
                    except discord.HTTPException as e:
                        print(f"❌ Ошибка HTTP: {e.status} - {e}")
                        if e.status == 401:
                            print("💡 Токен недействителен или истек. Получите новый токен.")
                    except Exception as e:
                        print(f"❌ Ошибка подключения: {e}")
                    return

                if not client_task.done():
                    client_task.cancel()
            except discord.LoginFailure:
                logger.error("❌ Неверный токен! Проверьте правильность user token.")
                logger.error("💡 Убедитесь, что вы используете USER token, а не bot token.")
                return
            except discord.HTTPException as e:
                logger.error(f"❌ Ошибка HTTP: {e.status} - {e}")
                return
            except Exception as e:
                logger.error(f"❌ Ошибка подключения: {e}")
                return

            if selected_guild_id:
                # Запуск расширенного ультра-быстрого сканирования
                scanner = EnhancedUltraFastScanner(selected_guild_id)
                # Запускаем расширенное ультра-быстрое сканирование
                # Режимы: "normal" (обычный) или "turbo" (максимальная скорость)
                await scanner.run_enhanced_ultra_fast_scan(token, mode="turbo")
            else:
                logger.error("❌ Сервер не выбран!")

        except KeyboardInterrupt:
            logger.info("\n👋 Остановлено пользователем")
        except Exception as e:
            logger.error(f"❌ Ошибка: {e}")


async def main():
    monitor = EnhancedUltraFastMonitor()
    await monitor.run()


if __name__ == "__main__":
    asyncio.run(main())